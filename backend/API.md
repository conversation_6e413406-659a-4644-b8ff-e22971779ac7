# API Documentation

## Authentication Routes

### POST /auth/signup
- **Description**: Register a new user
- **Request Body**:
  ```json
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "user" // Optional, defaults to "user"
  }
  ```
- **Response**: User object with JWT token

### POST /auth/signin
- **Description**: Login a user
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Response**: User object with JWT token

### GET /auth/signout
- **Description**: Logout a user
- **Response**: Success message

### GET /auth/me
- **Description**: Get current logged in user
- **Headers**: Authorization: Bearer {token}
- **Response**: User object

## User Routes

### GET /users/profile
- **Description**: Get the current user's profile
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c80",
      "name": "<PERSON> <PERSON>e",
      "email": "<EMAIL>",
      "role": "user",
      "createdAt": "2023-06-01T12:00:00.000Z"
    }
  }
  ```

### PUT /users/profile
- **Description**: Update the current user's profile
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "name": "John Smith",
    "email": "<EMAIL>"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c80",
      "name": "John Smith",
      "email": "<EMAIL>",
      "role": "user",
      "createdAt": "2023-06-01T12:00:00.000Z"
    }
  }
  ```

### PUT /users/change-password
- **Description**: Change the current user's password
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "currentPassword": "oldpassword123",
    "newPassword": "newpassword456"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Password updated successfully"
  }
  ```

## Prompt Routes

### GET /prompts
- **Description**: Get all prompts for the logged-in user
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "count": 3,
    "data": [
      {
        "_id": "60d21b4667d0d8992e610c90",
        "name": "Default System Prompt",
        "type": "system",
        "content": "You are an AI assistant for our IVR system...",
        "description": "Default system prompt for the IVR system",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      },
      {
        "_id": "60d21b4667d0d8992e610c91",
        "name": "Default Agent Prompt",
        "type": "agent",
        "content": "Hello! Thank you for calling...",
        "description": "Default agent greeting",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      }
    ]
  }
  ```

### GET /prompts/type/:type
- **Description**: Get prompts by type (system, agent, or functional)
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "count": 1,
    "data": [
      {
        "_id": "60d21b4667d0d8992e610c90",
        "name": "Default System Prompt",
        "type": "system",
        "content": "You are an AI assistant for our IVR system...",
        "description": "Default system prompt for the IVR system",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      }
    ]
  }
  ```

### GET /prompts/active
- **Description**: Get active prompts of each type
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "system": {
        "_id": "60d21b4667d0d8992e610c90",
        "name": "Default System Prompt",
        "type": "system",
        "content": "You are an AI assistant for our IVR system...",
        "description": "Default system prompt for the IVR system",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      },
      "agent": {
        "_id": "60d21b4667d0d8992e610c91",
        "name": "Default Agent Prompt",
        "type": "agent",
        "content": "Hello! Thank you for calling...",
        "description": "Default agent greeting",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      },
      "functional": {
        "_id": "60d21b4667d0d8992e610c92",
        "name": "Default Functional Prompt",
        "type": "functional",
        "content": "Process the following information...",
        "description": "Default functional prompt",
        "isActive": true,
        "user": "60d21b4667d0d8992e610c80",
        "createdAt": "2023-06-01T12:00:00.000Z",
        "updatedAt": "2023-06-01T12:00:00.000Z"
      }
    }
  }
  ```

### GET /prompts/:id
- **Description**: Get a single prompt by ID
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c90",
      "name": "Default System Prompt",
      "type": "system",
      "content": "You are an AI assistant for our IVR system...",
      "description": "Default system prompt for the IVR system",
      "isActive": true,
      "user": "60d21b4667d0d8992e610c80",
      "createdAt": "2023-06-01T12:00:00.000Z",
      "updatedAt": "2023-06-01T12:00:00.000Z"
    }
  }
  ```

### POST /prompts
- **Description**: Create a new prompt
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "name": "New System Prompt",
    "type": "system",
    "content": "You are an AI assistant for our IVR system...",
    "description": "New system prompt for the IVR system"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c93",
      "name": "New System Prompt",
      "type": "system",
      "content": "You are an AI assistant for our IVR system...",
      "description": "New system prompt for the IVR system",
      "isActive": false,
      "user": "60d21b4667d0d8992e610c80",
      "createdAt": "2023-06-01T12:00:00.000Z",
      "updatedAt": "2023-06-01T12:00:00.000Z"
    }
  }
  ```

### PUT /prompts/:id
- **Description**: Update a prompt
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "name": "Updated System Prompt",
    "content": "You are an updated AI assistant for our IVR system...",
    "description": "Updated system prompt for the IVR system"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c90",
      "name": "Updated System Prompt",
      "type": "system",
      "content": "You are an updated AI assistant for our IVR system...",
      "description": "Updated system prompt for the IVR system",
      "isActive": true,
      "user": "60d21b4667d0d8992e610c80",
      "createdAt": "2023-06-01T12:00:00.000Z",
      "updatedAt": "2023-06-01T13:00:00.000Z"
    }
  }
  ```

### PATCH /prompts/:id/activate
- **Description**: Set a prompt as active (deactivates all other prompts of the same type)
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "_id": "60d21b4667d0d8992e610c93",
      "name": "New System Prompt",
      "type": "system",
      "content": "You are an AI assistant for our IVR system...",
      "description": "New system prompt for the IVR system",
      "isActive": true,
      "user": "60d21b4667d0d8992e610c80",
      "createdAt": "2023-06-01T12:00:00.000Z",
      "updatedAt": "2023-06-01T13:00:00.000Z"
    }
  }
  ```

### DELETE /prompts/:id
- **Description**: Delete a prompt (cannot delete active prompts)
- **Authentication**: Required (Bearer token)
- **Response**:
  ```json
  {
    "success": true,
    "data": {}
  }
  ```

## Voice Routes

### GET /voices
- **Description**: Get all voices for the logged-in user
- **Authentication**: Required (Bearer token)
- **Response**: Array of voice objects

### POST /voices
- **Description**: Create a new voice
- **Authentication**: Required (Bearer token)
- **Response**: Created voice object

### GET /voices/:id
- **Description**: Get a single voice by ID
- **Authentication**: Required (Bearer token)
- **Response**: Voice object

### PUT /voices/:id
- **Description**: Update a voice
- **Authentication**: Required (Bearer token)
- **Response**: Updated voice object

### DELETE /voices/:id
- **Description**: Delete a voice
- **Authentication**: Required (Bearer token)
- **Response**: Success message

### PATCH /voices/:id/activate
- **Description**: Set a voice as active
- **Authentication**: Required (Bearer token)
- **Response**: Activated voice object

### POST /voices/elevenlabs/activate
- **Description**: Activate an ElevenLabs voice
- **Authentication**: Required (Bearer token)
- **Response**: Activated voice object

## ElevenLabs Routes

### GET /elevenlabs/voices
- **Description**: Get all available ElevenLabs voices
- **Authentication**: Required (Bearer token)
- **Response**: Array of ElevenLabs voice objects

### GET /elevenlabs/voices/:id/verify
- **Description**: Verify an ElevenLabs voice ID
- **Authentication**: Required (Bearer token)
- **Response**: Verification result

### GET /elevenlabs/settings
- **Description**: Get ElevenLabs settings
- **Authentication**: Required (Bearer token)
- **Response**: ElevenLabs settings object

### GET /elevenlabs/models
- **Description**: Get available ElevenLabs models
- **Authentication**: Required (Bearer token)
- **Response**: Array of ElevenLabs model objects

## API Key Routes

### GET /settings/api-keys
- **Description**: Get all API keys for the logged-in user
- **Authentication**: Required (Bearer token)
- **Response**: API keys object

### PUT /settings/api-keys
- **Description**: Update API keys
- **Authentication**: Required (Bearer token)
- **Request Body**: API keys object
- **Response**: Updated API keys object

### POST /settings/api-keys/validate/openai
- **Description**: Validate an OpenAI API key
- **Authentication**: Required (Bearer token)
- **Request Body**: OpenAI API key
- **Response**: Validation result

### GET /settings/api-keys/openai-models
- **Description**: Get available OpenAI models
- **Authentication**: Required (Bearer token)
- **Response**: Array of OpenAI model objects

## Call History Routes

### GET /call-history
- **Description**: Get call history for the logged-in user
- **Authentication**: Required (Bearer token)
- **Response**: Array of call history entries

### POST /call-history
- **Description**: Create a new call history entry
- **Authentication**: Required (Bearer token)
- **Request Body**: Call history entry
- **Response**: Created call history entry

### GET /call-history/stats
- **Description**: Get call statistics
- **Authentication**: Required (Bearer token)
- **Response**: Call statistics object

### GET /call-history/:id
- **Description**: Get a single call history entry by ID
- **Authentication**: Required (Bearer token)
- **Response**: Call history entry

### PUT /call-history/:id
- **Description**: Update a call history entry
- **Authentication**: Required (Bearer token)
- **Request Body**: Updated call history entry
- **Response**: Updated call history entry

### DELETE /call-history/:id
- **Description**: Delete a call history entry
- **Authentication**: Required (Bearer token)
- **Response**: Success message

## Recording Routes

### GET /recordings
- **Description**: Get all recordings for the logged-in user
- **Authentication**: Required (Bearer token)
- **Response**: Array of recording objects

### GET /recordings/:id
- **Description**: Get a single recording by ID
- **Authentication**: Required (Bearer token)
- **Response**: Recording object

### GET /recordings/:id/download
- **Description**: Download a recording
- **Authentication**: Required (Bearer token)
- **Response**: Recording file

### GET /recordings/:id/stream
- **Description**: Stream a recording
- **Authentication**: Required (Bearer token)
- **Response**: Recording stream

## Zoom Routes

### POST /zoom
- **Description**: Create a Zoom meeting with SIP access and optionally join with AI agent
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "topic": "Meeting Topic",
    "agenda": "Meeting Agenda",
    "start_time": "2023-05-01T10:00:00Z",
    "duration": 60,
    "timezone": "UTC",
    "join_with_agent": true,
    "caller_id": "+15551234567"
  }
  ```
- **Response**: Meeting details with SIP information and agent call status

### GET /zoom/logs
- **Description**: Get all Zoom meeting logs
- **Authentication**: Required (Bearer token)
- **Response**: Array of Zoom meeting logs

### GET /zoom/:meetingId
- **Description**: Get SIP information for an existing Zoom meeting
- **Authentication**: Required (Bearer token)
- **Response**: Meeting SIP information

### POST /zoom/join
- **Description**: Join an existing Zoom meeting with the AI agent
- **Authentication**: Required (Bearer token)
- **Request Body**:
  ```json
  {
    "meeting_id": "123456789",
    "passcode": "123456",
    "caller_id": "+15551234567"
  }
  ```
- **Response**: Meeting details with SIP information and agent call status

const http = require('http');
const dotenv = require('dotenv');
const express = require('express');
const cors = require('cors');
const WebSocketServer = require('websocket').server;
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const morgan = require('morgan');
const { logger } = require('./utils');
const connectDB = require('./config/db');

// Global API key variables
global.OPENAI_API_KEY = '';
global.ELEVENLABS_API_KEY = '';
global.DEEPGRAM_API_KEY = '';

dotenv.config();

const ESLController = require('./controller/ESLController');

const app = express();
app.use(cors({ origin: '*' }));
app.use(bodyParser.json());
app.use(cookieParser());

// Set up Morgan logger
// Define custom token for request body logging with sensitive data masking
morgan.token('req-body', (req) => {
  if (!req.body) return '{}';

  // Create a copy of the request body to avoid modifying the original
  const sanitizedBody = { ...req.body };

  // Mask sensitive fields
  const sensitiveFields = ['password', 'currentPassword', 'newPassword', 'token', 'apiKey', 'secret'];

  sensitiveFields.forEach(field => {
    if (sanitizedBody[field]) {
      sanitizedBody[field] = '********';
    }
  });

  return JSON.stringify(sanitizedBody);
});

// Development logging format with request bodies
const devLogFormat = ':method :url :status :response-time ms - :res[content-length] - :req-body';

// Production logging format (more concise)
const prodLogFormat = ':remote-addr - :method :url :status :response-time ms - :res[content-length]';

// Use appropriate format based on environment
const logFormat = process.env.NODE_ENV === 'production' ? prodLogFormat : devLogFormat;

// Skip logging for static assets or health checks if needed
const skipLog = (req, res) => {
  // Skip logging for static assets
  if (req.url.startsWith('/records')) return true;
  // Skip logging for health checks
  if (req.url === '/api/health') return true;
  return false;
};

// Apply Morgan middleware
app.use(morgan(logFormat, {
  skip: skipLog,
  stream: {
    write: (message) => {
      // Remove newline character and log using our logger
      logger.http(message.trim());
    }
  }
}));

// Serve static recording files with proper headers
app.use("/records", (req, res, next) => {
  // Set cache control headers for better performance
  res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day
  res.setHeader('Content-Type', 'audio/wav');
  next();
}, express.static('/zoom_bot/records'));

const server = http.createServer(app);

global.callCount = 0;
global.OngoingCall = 1;
global.CompletedCall = 2;


// Connect to MongoDB and initialize API keys
const initializeApp = async () => {
  try {
    const { getApiKeysFromDB } = require('./utils');
    // Connect to MongoDB
    await connectDB();

    // Load API keys from database
    const apiKeys = await getApiKeysFromDB();

    // Set global API key variables
    global.OPENAI_API_KEY = apiKeys.openaiApiKey || process.env.OPENAI_API_KEY || '';
    global.ELEVENLABS_API_KEY = apiKeys.elevenlabsApiKey || process.env.ELEVENLABS_API_KEY || '';
    global.DEEPGRAM_API_KEY = apiKeys.deepgramApiKey || process.env.DEEPGRAM_API_KEY || '';

    logger.info('API keys loaded from database');

    // Log which keys are available (without showing the actual keys)
    logger.info(`OpenAI API key: ${global.OPENAI_API_KEY ? 'Available' : 'Not available'}`);
    logger.info(`ElevenLabs API key: ${global.ELEVENLABS_API_KEY ? 'Available' : 'Not available'}`);
    logger.info(`Deepgram API key: ${global.DEEPGRAM_API_KEY ? 'Available' : 'Not available'}`);
  } catch (error) {
    logger.error('Error initializing application:', error);
    // Continue with environment variables as fallback
    global.OPENAI_API_KEY = process.env.OPENAI_API_KEY || '';
    global.ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY || '';
    global.DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY || '';
  }

  const { MediaStream } = require('./controller/mediaStream');

  const mediaws = new WebSocketServer({
    httpServer: server,
    autoAcceptConnections: true,
  });

  // Global variable for mediaStream
  let mediaStream;

  mediaws.on("connect", function (connection) {
    logger.info("FreeSWITCH: Client connected".gray);
    connection.on("error", (error) => {
      logger.error("Connection Error:", error);
    });
    mediaStream = new MediaStream(connection, "", "");
  });

  const authRoutes = require('./routes/authRoutes');
  const userRoutes = require('./routes/userRoutes');
  const promptRoutes = require('./routes/promptRoutes');
  const voiceRoutes = require('./routes/voiceRoutes');
  const callHistoryRoutes = require('./routes/callHistoryRoutes');
  const apiKeyRoutes = require('./routes/apiKeyRoutes');
  const elevenLabsRoutes = require('./routes/elevenLabsRoutes');
  const recordingRoutes = require('./routes/recordingRoutes');
  const zoomRoutes = require('./routes/zoomRoutes');
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/prompts', promptRoutes);
  app.use('/api/voices', voiceRoutes);
  app.use('/api/call-history', callHistoryRoutes);
  app.use('/api/settings/api-keys', apiKeyRoutes);
  app.use('/api/elevenlabs', elevenLabsRoutes);
  app.use('/api/recordings', recordingRoutes);
  app.use('/api/zoom', zoomRoutes);

  server.listen(process.env.PORT, () => {
    ESLController.init();
    logger.info(`Server listening on port ${process.env.PORT}`.gray);
  });
};

// Initialize the application
initializeApp();
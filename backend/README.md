# Callable Deepgram <> Epic Streaming Voice Agent

This is a basic server application that show cases end to end streaming voice agent

* Callable Phone Number
* Streaming freeSWITCH - Inbound Audio
* Streaming Deepgram - Speech to Text
* Streaming OpenAI - LLM
* Streaming Deepgram - Text to Speech
* Streaming freeSWITCH - Outbound Audio

## App sever setup


### Set environment variables

 You will need to set environment variables for your shell session. These environment variables are used to store the API keys required for authentication when accessing the OpenAI and Deepgram APIs.

 Open a Terminal and run:

```sh
export OPENAI_API_KEY=xxx
export DEEPGRAM_API_KEY=xxx
```

To verify the environment variables are set, run the following commands in your Terminal:

```sh
echo $OPENAI_API_KEY
echo $DEEPGRAM_API_KEY
```

### Installation

**Requires Node >= v12.1.0**

Run `npm install`

npm dependencies (contained in the `package.json`):
* websocket

#### Running the server

Start with `npm run start`
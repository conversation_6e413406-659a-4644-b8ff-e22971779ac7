const OpenAI = require('openai');
const dotenv = require('dotenv');
const fs = require('fs');

dotenv.config();

const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY
});

const { validate_address_prompt } = require('./controller/prompts');

// Helper functions
const get_weather_of_city = (city) => {
    const CITY_WEAHTER_LIST = {
        "New York": "sunny",
        "Los Angeles": "cloudy",
        "Tokyo": "rainy"
    }
    return `${CITY_WEAHTER_LIST[city]}`;
}

const hangup_call = () => {
    return "Thank you for calling! Goodbye!";
}

const change_language = (language) => {
    return `Changed language to ${language}`;
}

const address_validation = async (address) => {
    const systemPrompt = validate_address_prompt;
    const response = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [{role: 'system', content: systemPrompt}, 
            { role: 'user', content: `address: ${address}` }],
    });
    return response.choices[0].message.content;
}

// Configuration
const completion_payload = {
    model: "gpt-4o",
    messages: [
        { role: "system", content: "You are a helpful voice ai assistant that can answer questions and provide information."},
        { role: "user", content: "Hello! How are you?" },
        { role: "assistant", content: "How can I help you?" },
        { role: "user", content: "SuTE 82 HEMLOCK LANE, OSWEGO, ILLINOIS 60543 and also please tell me the weather in here" },
        // { role: "assistant", content: "How can I help you?"},
        // { role: "user", content: "I want to know the weather in New York, Chicago, and Tokyo" },
    ],
    tools: [
        {
            type: "function",
            function: {
                name: "get_weather_of_city",
                description: "Get the weather of a city",
                parameters: {
                    type: "object",
                    properties: {
                        city: { type: "string" }
                    }
                },
                required: ["city"],
                additionalProperties: false
            }
        },
        {
            type: "function",
            function: {
                name: "hangup_call",
                description: "Hang up the call",
            }
        },
        {
            type: "function",
            function: {
                name: "change_language",
                description: "Change the language of the call",
                parameters: {
                    type: "object",
                    properties: {
                        language: { type: "string" }
                    }
                },
                required: ["language"],
                additionalProperties: false
            }
        },
        {
            type: "function",
            function: {
                name: "address_validation",
                description: "Validate the address",
                parameters: {
                    type: "object",
                    properties: {
                        address: { type: "string" }
                    }
                },
                required: ["address"],
                additionalProperties: false
            }
        }
    ]
};  

// Content handling functions
async function handleAssistantContent(delta, accumulatedContent) {
    if (delta.content) {
        accumulatedContent += delta.content;
    }
    return accumulatedContent;
}

function processToolCall(toolCall, toolCalls) {
    const currentId = toolCall.id;
    if (!toolCalls.includes(currentId) && currentId !== undefined) {
        let temp = {
            index: toolCall.index,
            id: currentId,
            arguments: toolCall.function?.arguments || ""
        };
        if (toolCall.function?.name) {
            temp.function_name = toolCall.function.name;
            // console.log("Function name:", toolCall.function.name);
        }
        toolCalls.push(temp);
    } else {
        // console.log("Tool Call:", toolCall);
        let tool_item = toolCalls.find(call => call.index === toolCall.index);
        tool_item.arguments += toolCall.function?.arguments || "";
    }
}

// Tool execution functions
async function executeToolFunction(functionName, args) {
    let toolResult;
    switch(functionName) {
        case "get_weather_of_city":
            toolResult = get_weather_of_city(args.city);
            break;
        case "hangup_call":
            toolResult = hangup_call();
            break;
        case "change_language":
            toolResult = change_language(args.language);
            break;
        case "address_validation":
            toolResult = await address_validation(args.address);
            break;
    }
    return toolResult;
}

function createToolCallPayload(toolCall) {
    return {
        id: toolCall.id,
        function: {
            name: toolCall.function_name,
            arguments: JSON.stringify(toolCall.arguments)
        },
        type: "function"
    };
}

async function processToolResults(toolCalls) {
    return await Promise.all(toolCalls.map(async (toolCall) => {
        const functionName = toolCall.function_name;
        const args = JSON.parse(toolCall.arguments);
        const toolResult = await executeToolFunction(functionName, args);
        
        // console.log(`Tool result for ${functionName}:`, toolResult);
        return {
            role: "tool",
            content: JSON.stringify(toolResult),
            tool_call_id: toolCall.id
        };
    }));
}

async function handleToolCalls(toolCalls) {
    // console.log("Processing toolCalls:", toolCalls);
    const toolResults = await processToolResults(toolCalls);
    
    let toolcall_payload = {
        role: "assistant",
        content: null,
        tool_calls: toolCalls.map(createToolCallPayload)
    };

    completion_payload.messages.push(toolcall_payload);
    completion_payload.messages.push(...toolResults);
}

// Main response handling
async function getResponse() {
    completion_payload.stream = true;
    const responseStream = await openai.chat.completions.create(completion_payload);

    let toolCalls = [];
    let accumulatedContent = "";

    for await (const chunk of responseStream) {
        const delta = chunk.choices[0].delta;
        const finishReason = chunk.choices[0].finish_reason;

        // Handle text content
        if (delta.content) {
            accumulatedContent = await handleAssistantContent(delta, accumulatedContent);
            console.log("Assistant:", accumulatedContent);
        }

        // Handle tool calls
        if (delta.tool_calls) {
            delta.tool_calls.forEach(toolCall => processToolCall(toolCall, toolCalls));
        }

        // Process tool calls when complete
        if (finishReason === 'tool_calls' && toolCalls.length > 0 && toolCalls.every(call => call.arguments && call.function_name)) {
            await handleToolCalls(toolCalls);
            toolCalls = [];
            getResponse();
        }

        // Handle completion of text response
        if (finishReason === 'stop' && accumulatedContent) {
            console.log("Final accumulated content:", accumulatedContent);
            return accumulatedContent;
        }
    }
}

getResponse();
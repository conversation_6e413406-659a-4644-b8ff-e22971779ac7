const ApiKey = require('../models/ApiKey');
const { logger } = require('./index');

/**
 * Get OpenAI settings from the database (system-wide)
 * @returns {Promise<Object>} - The OpenAI settings
 */
const getOpenAISettings = async () => {
  try {
    // Find the first API key entry (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      // Return default settings if no API keys exist
      return {
        model: 'gpt-3.5-turbo'
      };
    }

    // Return the OpenAI settings
    return {
      model: apiKeys.openaiModel || 'gpt-3.5-turbo'
    };
  } catch (error) {
    logger.error('Error getting OpenAI settings:', error);
    // Return default settings on error
    return {
      model: 'gpt-3.5-turbo'
    };
  }
};

module.exports = getOpenAISettings;

const fs = require('fs');
const path = require('path');

exports.getLanguageCode = (languageName) => {
    const languageMap = {
        "english": "en",
        "spanish": "es",
        "portuguese": "pt",
        "chinese": "zh",
        "japanese": "ja",
        "bulgarian": "bg",
        "romanian": "ro",
        "arabic": "ar",
        "czech": "cs",
        "finnish": "fi",
        "croatian": "hr",
        "french": "fr",
        "malay": "ms",
        "slovak": "sk",
        "danish": "da",
        "tamil": "ta",
        "ukrainian": "uk",
        "russian": "ru",
        "german": "de",
        "turkish": "tr",
        "polish": "pl",
        "italian": "it",
    };

    return languageMap[languageName.toLowerCase()];
}

exports.getLanguageFromCode = (languageCode) => {
    const languageMap = {
        "en": "english",
        "es": "spanish",
        "pt": "portuguese",
        "zh": "chinese",
        "ja": "japanese",
        "bg": "bulgarian",
        "ro": "romanian",
        "ar": "arabic",
        "cs": "czech",
        "fi": "finnish",
        "hr": "croatian",
        "fr": "french",
        "ms": "malay",
        "sk": "slovak",
        "da": "danish",
        "ta": "tamil",
        "uk": "ukrainian",
        "ru": "russian",
        "de": "german",
        "tr": "turkish",
        "pl": "polish",
        "it": "italian",
    };
    return languageMap[languageCode];
}

class Logger {
    constructor() {
    }

    log(...messages) {
        this.writeLog('LOG', ...messages);
    }

    debug(...messages) {
        this.writeLog('DEBUG', ...messages);
    }

    info(...messages) {
        this.writeLog('INFO', ...messages);
    }

    warn(...messages) {
        this.writeLog('WARN', ...messages);
    }

    error(...messages) {
        this.writeLog('ERROR', ...messages);
    }

    http(...messages) {
        this.writeLog('HTTP', ...messages);
    }

    writeLog(level, ...messages) {
        try {
            for (const message of messages) {
                const timestamp = new Date().toISOString();
                const logMessage = `[${timestamp}] [${level}] ${message}`;

                // Write to console
                console.log(logMessage);
            }
        } catch (e) {
            console.log(e);
        }
    }
}

exports.logger = new Logger();

// Import utility functions
const getDeepgramSettings = require('./getDeepgramSettings');
const getElevenLabsSettings = require('./getElevenLabsSettings');
const getActiveVoice = require('./getActiveVoice');
const getAgentPrompt = require('./getAgentPrompt');
const getOpenAISettings = require('./getOpenAISettings');
const getApiKeysFromDB = require('./getApiKeysFromDB');

exports.getDeepgramSettings = getDeepgramSettings;
exports.getElevenLabsSettings = getElevenLabsSettings;
exports.getActiveVoice = getActiveVoice;
exports.getAgentPrompt = getAgentPrompt;
exports.getOpenAISettings = getOpenAISettings;
exports.getApiKeysFromDB = getApiKeysFromDB;
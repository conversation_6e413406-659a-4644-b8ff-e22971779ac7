const Voice = require('../models/Voice');
const { logger } = require('./index');

/**
 * Get the active voice from the database (system-wide)
 * @returns {Promise<Object|null>} - The active voice or null if none exists
 */
const getActiveVoice = async () => {
  try {
    // Find the active voice (system-wide)
    const activeVoice = await Voice.findOne({
      isActive: true
    });

    if (!activeVoice) {
      logger.warn('No active voice found in the system');
      return null;
    }

    return activeVoice;
  } catch (error) {
    logger.error('Error getting active voice:', error);
    return null;
  }
};

module.exports = getActiveVoice;

const ApiKey = require('../models/ApiKey');
const { logger } = require('./index');

/**
 * Get all API keys from the database (system-wide)
 * @returns {Promise<Object>} - All API keys
 */
const getApiKeysFromDB = async () => {
  try {
    // Find the first API key entry (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      // Return empty strings if no API keys exist
      logger.warn('No API keys found in the database');
      return {
        openaiApiKey: '',
        elevenlabsApiKey: '',
        deepgramApiKey: ''
      };
    }

    // Return the API keys
    return {
      openaiApiKey: apiKeys.openaiApiKey || '',
      elevenlabsApiKey: apiKeys.elevenlabsApiKey || '',
      deepgramApiKey: apiKeys.deepgramApiKey || ''
    };
  } catch (error) {
    logger.error('Error getting API keys from database:', error);
    // Return empty strings on error
    return {
      openaiApiKey: '',
      elevenlabsApiKey: '',
      deepgramApiKey: ''
    };
  }
};

module.exports = getApiKeysFromDB;
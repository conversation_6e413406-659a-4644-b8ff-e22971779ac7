const ApiKey = require('../models/ApiKey');
const { logger } = require('./index');

/**
 * Get Deepgram settings from the database (system-wide)
 * @returns {Promise<Object>} - The Deepgram settings
 */
const getDeepgramSettings = async () => {
  try {
    // Find the first API key entry (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      // Return default settings if no API keys exist
      return {
        language: 'en',
        utterance: 2000,
        interruption: 500
      };
    }

    // Return the Deepgram settings
    return {
      language: apiKeys.deepgramLanguage || 'en',
      utterance: apiKeys.deepgramUtterance || 2000,
      interruption: apiKeys.deepgramInterruption || 500
    };
  } catch (error) {
    logger.error('Error getting Deepgram settings:', error);
    // Return default settings on error
    return {
      language: 'en',
      utterance: 2000,
      interruption: 500
    };
  }
};

module.exports = getDeepgramSettings;

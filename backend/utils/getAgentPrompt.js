const Prompt = require('../models/Prompt');
const { logger } = require('./index');
const { translator_prompt } = require('./prompts');

/**
 * Get the agent prompt from the database
 * @returns {Promise<Object>} - Object containing prompt
 */
const getAgentPrompt = async () => {
  try {
    // Find the active agent prompt
    const agentPromptDoc = await Prompt.findOne({
      type: 'agent',
      isActive: true
    }).sort({ updatedAt: -1 });

    if (!agentPromptDoc) {
      logger.warn('No active agent prompt found in the system, using default translator prompt');
      return {
        prompt: translator_prompt
      };
    }

    try {
      // Try to parse the content as JSON
      const parsedContent = JSON.parse(agentPromptDoc.content);
      return {
        prompt: parsedContent.prompt || translator_prompt
      };
    } catch (parseError) {
      // If parsing fails, assume it's just a prompt without JSON structure (old format)
      logger.warn('Agent prompt content is not in JSON format, using as prompt only');
      return {
        prompt: agentPromptDoc.content
      };
    }
  } catch (error) {
    logger.error('Error getting agent prompt:', error);
    // Return default values if there's an error
    return {
      prompt: translator_prompt
    };
  }
};

module.exports = getAgentPrompt;

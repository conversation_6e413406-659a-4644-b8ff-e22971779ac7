const ApiKey = require('../models/ApiKey');
const { logger } = require('./index');

/**
 * Get ElevenLabs settings from the database (system-wide)
 * @returns {Promise<Object>} - The ElevenLabs settings
 */
const getElevenLabsSettings = async () => {
  try {
    // Find the first API key entry (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      // Return default settings if no API keys exist
      return {
        stability: 0.5,
        similarity: 0.8,
        exaggeration: 0.0
      };
    }

    // Return the ElevenLabs settings
    return {
      stability: apiKeys.elevenlabsStability || 0.5,
      similarity: apiKeys.elevenlabsSimilarity || 0.8,
      exaggeration: apiKeys.elevenlabsExaggeration || 0.0
    };
  } catch (error) {
    logger.error('Error getting ElevenLabs settings:', error);
    // Return default settings on error
    return {
      stability: 0.5,
      similarity: 0.8,
      exaggeration: 0.0
    };
  }
};

module.exports = getElevenLabsSettings;

const express = require('express');
const router = express.Router();
const {
  getPrompts,
  getPromptsByType,
  getActivePrompts,
  getPrompt,
  createPrompt,
  updatePrompt,
  activatePrompt,
  deletePrompt
} = require('../controllers/promptController');
const { protect } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getPrompts)
  .post(createPrompt);

router.route('/active')
  .get(getActivePrompts);

router.route('/type/:type')
  .get(getPromptsByType);

router.route('/:id')
  .get(getPrompt)
  .put(updatePrompt)
  .delete(deletePrompt);

router.route('/:id/activate')
  .patch(activatePrompt);

module.exports = router;

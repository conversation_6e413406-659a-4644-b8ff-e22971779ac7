const express = require('express');
const router = express.Router();
const {
  getVoices,
  getSettings,
  getModels,
  verifyVoiceId
} = require('../controllers/elevenLabsController');
const { protect } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Routes
router.route('/voices')
  .get(getVoices);

router.route('/voices/:id/verify')
  .get(verifyVoiceId);

router.route('/settings')
  .get(getSettings);

router.route('/models')
  .get(getModels);

module.exports = router;

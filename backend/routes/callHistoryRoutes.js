const express = require('express');
const router = express.Router();
const {
  getCallHistory,
  getCallHistoryEntry,
  createCallHistoryEntry,
  updateCallHistoryEntry,
  deleteCallHistoryEntry,
  getCallStats
} = require('../controllers/callHistoryController');
const { protect } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getCallHistory)
  .post(createCallHistoryEntry);

router.route('/stats')
  .get(getCallStats);

router.route('/:id')
  .get(getCallHistoryEntry)
  .put(updateCallHistoryEntry)
  .delete(deleteCallHistoryEntry);

module.exports = router;

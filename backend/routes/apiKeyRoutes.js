const express = require('express');
const router = express.Router();
const {
  getApiKeys,
  updateApi<PERSON>eys,
  validateOpenAIKey,
  getOpenAIModels
} = require('../controllers/apiKeyController');
const { protect } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getApiKeys)
  .put(updateApiKeys);

router.route('/validate/openai')
  .post(validateOpenAIKey);

router.route('/openai-models')
  .get(getOpenAIModels);

module.exports = router;

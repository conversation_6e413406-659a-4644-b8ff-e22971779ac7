const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const { createZoomMeeting, getZoomMeetingSIPInfo, getZoomMeetingSIPInfoWithPasscode } = require('../controllers/zoomController');
const ESLController = require("../controller/ESLController");
const { logger } = require('../utils');
const ZoomMeeting = require('../models/ZoomMeeting');

// Protect all routes
router.use(protect);

/**
 * API endpoint to create a Zoom meeting with SIP access and optionally join with AI agent
 *
 * Request body:
 * {
 *   "topic": "Meeting Topic",
 *   "agenda": "Meeting Agenda",
 *   "start_time": "2023-05-01T10:00:00Z", // ISO format
 *   "duration": 60, // minutes
 *   "timezone": "UTC",
 *   "join_with_agent": true, // Whether to have the AI agent join the meeting
 *   "caller_id": "+15551234567" // Optional caller ID for the AI agent
 * }
 *
 */
router.post("/", async (req, res) => {
    try {
        // Get meeting details from request body
        const meetingDetails = req.body;
        const joinWithAgent = req.body.join_with_agent === true;
        const callerId = req.body.caller_id || '+15551234567';

        // Validate required fields
        if (!meetingDetails.topic) {
            return res.status(400).json({ error: "Meeting topic is required" });
        }

        // Set default values if not provided
        if (!meetingDetails.start_time) {
            meetingDetails.start_time = new Date(Date.now() + 300000).toISOString(); // 5 minutes from now
        }

        if (!meetingDetails.duration) {
            meetingDetails.duration = 60; // 60 minutes
        }

        if (!meetingDetails.timezone) {
            meetingDetails.timezone = "UTC";
        }

        // Ensure SIP settings are enabled
        if (!meetingDetails.settings) {
            meetingDetails.settings = {};
        }

        meetingDetails.settings.enable_phone_sip_uri = true;
        meetingDetails.settings.audio = "telephony";

        if (!meetingDetails.settings.telephony) {
            meetingDetails.settings.telephony = { third_party_audio: true };
        }

        // Create the meeting
        logger.info(`Creating Zoom meeting: ${meetingDetails.topic}`);
        const meeting = await createZoomMeeting(meetingDetails);

        // Get SIP information
        logger.info(`Getting SIP information for meeting ID: ${meeting.id}`);
        const sipInfo = await getZoomMeetingSIPInfo(meeting.id);

        // Prepare response object
        const response = {
            meeting: {
                id: meeting.id,
                topic: meeting.topic,
                start_time: meeting.start_time,
                duration: meeting.duration,
                join_url: meeting.join_url,
                password: meeting.password
            },
            sip: {
                uri: sipInfo.sip_uri || null,
                enabled: sipInfo.sip_enabled || false,
                invitation: sipInfo.invitation || null
            }
        };

        logger.info(`Zoom meeting created successfully with ID: ${meeting.id}`);

        // Have the AI agent join the meeting if requested and SIP URI is available
        if (joinWithAgent && sipInfo.sip_uri) {
            try {
                logger.info(`AI agent joining Zoom meeting: ${meeting.id}`);

                // Join the meeting via SIP
                const agentCallResult = await ESLController.joinZoomMeetingViaSIP({
                    sipUri: sipInfo.sip_uri,
                    passcode: meeting.password
                });

                // Add agent call information to the response
                response.agent_call = {
                    status: agentCallResult.status,
                    uuid: agentCallResult.uuid || null,
                    timestamp: new Date().toISOString()
                };

                logger.info(`AI agent joined Zoom meeting: ${meeting.id}`);
            } catch (error) {
                logger.error(`Error joining Zoom meeting with AI agent: ${error.message}`);
                response.agent_call = {
                    status: 'ERROR',
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
            }
        }

        // Store the meeting in the database
        try {
            const zoomMeeting = new ZoomMeeting({
                meeting: response.meeting,
                sip: response.sip,
                agent_call: response.agent_call,
                user: req.user.id
            });

            await zoomMeeting.save();
            logger.info(`Zoom meeting saved to database: ${meeting.id}`);
        } catch (dbError) {
            logger.error(`Error saving Zoom meeting to database: ${dbError.message}`);
            // Continue even if database save fails
        }

        // Return the response
        res.json(response);
    } catch (error) {
        logger.error(`Error creating Zoom meeting: ${error.message}`);
        res.status(500).json({
            error: error.message,
            details: error.response ? error.response.data : null
        });
    }
});

/**
 * API endpoint to get all Zoom meeting logs
 * GET /api/zoom/logs
 */
router.get("/logs", async (req, res) => {
    try {
        // Get all meetings for the current user, sorted by creation date (newest first)
        const meetings = await ZoomMeeting.find({ user: req.user.id })
            .sort({ createdAt: -1 })
            .limit(50); // Limit to the most recent 50 meetings

        res.json({
            success: true,
            count: meetings.length,
            data: meetings
        });
    } catch (error) {
        logger.error(`Error getting Zoom meeting logs: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API endpoint to get SIP information for an existing Zoom meeting
 * Response:
 * {
 *   "meeting": { ... meeting details ... },
 *   "sip": { ... SIP information ... },
 *   "agent_call": { // Only included if join_with_agent is true
 *     "status": "SUCCESS",
 *     "uuid": "call-uuid",
 *     "timestamp": "2023-05-01T10:00:00Z"
 *   }
 * }
 */
router.get("/:meetingId", async (req, res) => {
    try {
        const { meetingId } = req.params;

        if (!meetingId) {
            return res.status(400).json({ error: "Meeting ID is required" });
        }

        // Get SIP information
        logger.info(`Getting SIP information for meeting ID: ${meetingId}`);
        const sipInfo = await getZoomMeetingSIPInfo(meetingId);

        // Return SIP information
        res.json({
            meeting_id: meetingId,
            sip: {
                uri: sipInfo.sip_uri || null,
                enabled: sipInfo.sip_enabled || false,
                invitation: sipInfo.invitation || null
            }
        });
    } catch (error) {
        logger.error(`Error getting SIP information: ${error.message}`);
        res.status(500).json({
            error: error.message,
            details: error.response ? error.response.data : null
        });
    }
});

/**
 * API endpoint to join an existing Zoom meeting with the AI agent
 * POST /api/zoom/join
 *
 * Request body:
 * {
 *   "meeting_id": "123456789",
 *   "passcode": "123456",
 *   "caller_id": "+15551234567" // Optional caller ID for the AI agent
 * }
 *
 * Response:
 * {
 *   "meeting": { ... meeting details ... },
 *   "sip": { ... SIP information ... },
 *   "agent_call": {
 *     "status": "SUCCESS",
 *     "uuid": "call-uuid",
 *     "timestamp": "2023-05-01T10:00:00Z"
 *   }
 * }
 */
router.post("/join", async (req, res) => {
    try {
        // Get meeting details from request body
        const { meeting_id, passcode, caller_id } = req.body;

        // Validate required fields
        if (!meeting_id) {
            return res.status(400).json({ error: "Meeting ID is required" });
        }

        // Get SIP information for the meeting
        logger.info(`Getting SIP information for meeting ID: ${meeting_id}`);
        const sipInfo = await getZoomMeetingSIPInfoWithPasscode(meeting_id, passcode);

        // Prepare response object
        const response = {
            meeting: {
                id: sipInfo.id,
                topic: sipInfo.topic,
                join_url: sipInfo.join_url,
                password: sipInfo.password,
                // Add required fields for database validation
                start_time: new Date().toISOString(),
                duration: 60 // Default to 60 minutes for existing meetings
            },
            sip: {
                uri: sipInfo.sip_uri || null,
                enabled: sipInfo.sip_enabled || false,
                invitation: sipInfo.invitation || null
            }
        };

        // Check if SIP URI is available
        if (!sipInfo.sip_uri) {
            logger.error(`No SIP URI found for meeting ID: ${meeting_id}`);
            return res.status(400).json({
                error: "No SIP URI found for this meeting. Make sure SIP is enabled for the meeting."
            });
        }

        // Join the meeting with the AI agent
        try {
            logger.info(`AI agent joining Zoom meeting: ${meeting_id}`);

            // Join the meeting via SIP
            const agentCallResult = await ESLController.joinZoomMeetingViaSIP({
                sipUri: sipInfo.sip_uri,
                passcode: passcode
            });

            // Add agent call information to the response
            response.agent_call = {
                status: agentCallResult.status,
                uuid: agentCallResult.uuid || null,
                timestamp: new Date().toISOString()
            };

            // Only log success if the status is actually SUCCESS
            if (agentCallResult.status === 'SUCCESS') {
                logger.info(`AI agent joined Zoom meeting: ${meeting_id}`);
            } else {
                logger.error(`AI agent failed to join Zoom meeting: ${meeting_id} with status: ${agentCallResult.status}`);
            }
        } catch (error) {
            logger.error(`Error joining Zoom meeting with AI agent: ${error.message}`);
            response.agent_call = {
                status: 'ERROR',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }

        // Store the meeting in the database
        try {
            const zoomMeeting = new ZoomMeeting({
                meeting: response.meeting,
                sip: response.sip,
                agent_call: response.agent_call,
                user: req.user.id
            });

            await zoomMeeting.save();
            logger.info(`Zoom meeting saved to database: ${meeting_id}`);
        } catch (dbError) {
            logger.error(`Error saving Zoom meeting to database: ${dbError.message}`);
            // Continue even if database save fails
        }

        // Return the response
        res.json(response);
    } catch (error) {
        logger.error(`Error joining Zoom meeting: ${error.message}`);
        res.status(500).json({
            error: error.message,
            details: error.response ? error.response.data : null
        });
    }
});

module.exports = router;

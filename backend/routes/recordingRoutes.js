const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getRecordings,
  getRecording,
  downloadRecording,
  streamRecording
} = require('../controllers/recordingController');

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getRecordings);

router.route('/:id')
  .get(getRecording);

router.route('/:id/download')
  .get(downloadRecording);

router.route('/:id/stream')
  .get(streamRecording);

module.exports = router;

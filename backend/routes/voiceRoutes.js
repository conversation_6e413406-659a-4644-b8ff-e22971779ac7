const express = require('express');
const router = express.Router();
const {
  getVoices,
  getVoice,
  createVoice,
  updateVoice,
  activateVoice,
  activateElevenLabsVoice,
  deleteVoice
} = require('../controllers/voiceController');
const { protect, authorize } = require('../middleware/auth');

// Protect all routes
router.use(protect);

// Routes
router.route('/')
  .get(getVoices)
  .post(createVoice);

router.route('/elevenlabs/activate')
  .post(activateElevenLabsVoice);

router.route('/:id')
  .get(getVoice)
  .put(updateVoice)
  .delete(deleteVoice);

router.route('/:id/activate')
  .patch(activateVoice);

module.exports = router;

const mongoose = require('mongoose');

const CallHistorySchema = new mongoose.Schema({
  phoneNumber: {
    type: String,
    required: [true, 'Please add a phone number'],
    trim: true,
  },
  duration: {
    type: Number, // Duration in seconds
    default: 0,
  },
  wasTransferred: {
    type: Boolean,
    default: false,
  },
  transferredTo: {
    type: String,
    trim: true,
  },
  ivrAddress: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'IVRAddress',
  },
  firstName: {
    type: String,
    trim: true,
  },
  lastName: {
    type: String,
    trim: true,
  },
  callSummary: {
    type: String,
    trim: true,
  },
  callStartTime: {
    type: Date,
    default: Date.now,
  },
  callEndTime: {
    type: Date,
  },
  transcript: {
    type: String,
    trim: true,
  },
  status: {
    type: String,
    enum: ['progressing', 'completed', 'failed'],
    default: 'progressing'
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Virtual for formatted duration
CallHistorySchema.virtual('formattedDuration').get(function() {
  const seconds = this.duration;
  if (!seconds) return '0s';

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes === 0) {
    return `${remainingSeconds}s`;
  }

  return `${minutes}m ${remainingSeconds}s`;
});

// Set toJSON option to include virtuals
CallHistorySchema.set('toJSON', { virtuals: true });
CallHistorySchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('CallHistory', CallHistorySchema);

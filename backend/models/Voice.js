const mongoose = require('mongoose');

const VoiceSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
  },
  voiceId: {
    type: String,
    required: [true, 'Please add a voice ID'],
    trim: true,
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    trim: true,
  },
  previewUrl: {
    type: String,
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  settings: {
    stability: {
      type: Number,
      default: 0.75,
      min: 0,
      max: 1,
    },
    similarity_boost: {
      type: Number,
      default: 0.75,
      min: 0,
      max: 1,
    },
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('Voice', VoiceSchema);

const mongoose = require('mongoose');

const PromptSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
  },
  type: {
    type: String,
    required: [true, 'Please specify the prompt type'],
    enum: ['system', 'agent', 'functional'],
    default: 'system',
  },
  content: {
    type: String,
    required: [true, 'Please add prompt content'],
  },
  description: {
    type: String,
    trim: true,
  },
  isActive: {
    type: Boolean,
    default: true,
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  }
});

// Update the updatedAt field on save
PromptSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Prompt', PromptSchema);

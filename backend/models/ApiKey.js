const mongoose = require('mongoose');
const crypto = require('crypto');

// Encryption settings
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-fallback-encryption-key-32chars'; // 32 bytes key
const ENCRYPTION_ALGORITHM = 'aes-256-cbc';

// Helper functions for encryption/decryption
function encrypt(text) {
  if (!text) return '';

  try {
    // For very long API keys, we'll use a simpler approach
    if (text.length > 100) {
      // Simple obfuscation for very long keys
      // This is not as secure as proper encryption but will work for long keys
      const key = Buffer.from(ENCRYPTION_KEY).toString('hex');
      let result = '';
      for (let i = 0; i < text.length; i++) {
        result += String.fromCharCode(text.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return 'XOR:' + Buffer.from(result).toString('base64');
    }

    // Standard encryption for normal length keys
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, Buffer.from(ENCRYPTION_KEY), iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    return 'AES:' + iv.toString('hex') + ':' + encrypted.toString('hex');
  } catch (error) {
    console.error('Encryption error:', error);
    // Fallback to simple base64 encoding if encryption fails
    return 'B64:' + Buffer.from(text).toString('base64');
  }
}

function decrypt(text) {
  if (!text) return '';

  try {
    // Handle different encryption methods
    if (text.startsWith('XOR:')) {
      // Decrypt XOR encrypted text
      const encoded = text.substring(4); // Remove 'XOR:' prefix
      const encryptedText = Buffer.from(encoded, 'base64').toString();
      const key = Buffer.from(ENCRYPTION_KEY).toString('hex');
      let result = '';
      for (let i = 0; i < encryptedText.length; i++) {
        result += String.fromCharCode(encryptedText.charCodeAt(i) ^ key.charCodeAt(i % key.length));
      }
      return result;
    } else if (text.startsWith('B64:')) {
      // Decode base64 encoded text
      return Buffer.from(text.substring(4), 'base64').toString();
    } else if (text.startsWith('AES:')) {
      // Decrypt AES encrypted text
      const parts = text.substring(4).split(':'); // Remove 'AES:' prefix
      if (parts.length !== 2) return '';

      const iv = Buffer.from(parts[0], 'hex');
      const encryptedText = Buffer.from(parts[1], 'hex');
      const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, Buffer.from(ENCRYPTION_KEY), iv);
      let decrypted = decipher.update(encryptedText);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      return decrypted.toString();
    } else {
      // Legacy format without prefix (for backward compatibility)
      const textParts = text.split(':');
      if (textParts.length !== 2) return text; // If not in expected format, return as is

      const iv = Buffer.from(textParts[0], 'hex');
      const encryptedText = Buffer.from(textParts[1], 'hex');
      const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, Buffer.from(ENCRYPTION_KEY), iv);
      let decrypted = decipher.update(encryptedText);
      decrypted = Buffer.concat([decrypted, decipher.final()]);
      return decrypted.toString();
    }
  } catch (error) {
    console.error('Decryption error:', error);
    return ''; // Return empty string on error
  }
}

const ApiKeySchema = new mongoose.Schema({
  openaiApiKey: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  deepgramApiKey: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  deepgramLanguage: {
    type: String,
    default: 'en'
  },
  deepgramUtterance: {
    type: Number,
    default: 2000,
    min: 1001,
    max: 5000
  },
  deepgramInterruption: {
    type: Number,
    default: 500,
    min: 100,
    max: 1000
  },
  elevenlabsApiKey: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  elevenlabsStability: {
    type: Number,
    default: 0.5,
    min: 0,
    max: 1
  },
  elevenlabsSimilarity: {
    type: Number,
    default: 0.8,
    min: 0,
    max: 1
  },
  elevenlabsExaggeration: {
    type: Number,
    default: 0.0,
    min: 0,
    max: 1
  },
  elevenlabsModel: {
    type: String,
    default: 'eleven_multilingual_v2'
  },
  openaiModel: {
    type: String,
    default: 'gpt-3.5-turbo'
  },
  zoomClientId: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  zoomClientSecret: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  zoomAccountId: {
    type: String,
    set: encrypt,
    get: decrypt
  },
  zoomContactEmail: {
    type: String,
    default: ''
  },
  zoomContactName: {
    type: String,
    default: ''
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { getters: true, setters: false },
  toObject: { getters: true, setters: false }
});

// Update the updatedAt field on save
ApiKeySchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('ApiKey', ApiKeySchema);

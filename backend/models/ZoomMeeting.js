const mongoose = require('mongoose');

const ZoomMeetingSchema = new mongoose.Schema({
  meeting: {
    id: {
      type: String,
      required: true,
      unique: true
    },
    topic: {
      type: String,
      required: true
    },
    start_time: {
      type: String,
      required: true
    },
    duration: {
      type: Number,
      required: true
    },
    join_url: {
      type: String,
      required: true
    },
    password: {
      type: String
    }
  },
  sip: {
    uri: {
      type: String
    },
    enabled: {
      type: Boolean,
      default: false
    },
    invitation: {
      type: String
    }
  },
  agent_call: {
    status: {
      type: String,
      enum: ['SUCCESS', 'ERROR']
    },
    uuid: {
      type: String
    },
    timestamp: {
      type: String
    },
    error: {
      type: String
    }
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('ZoomMeeting', ZoomMeetingSchema);

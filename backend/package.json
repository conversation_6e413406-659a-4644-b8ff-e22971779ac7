{"name": "epic-streaming-voice-agent", "version": "0.0.1", "description": "Streaming Voice Agent of Epic Caller AI", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "webpack --config webpack.config.js"}, "repository": {"type": "git", "url": ""}, "author": "", "license": "MIT", "dependencies": {"@deepgram/sdk": "^3.5.0", "axios": "^1.7.2", "bcryptjs": "^3.0.2", "colors": "^1.4.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "elevenlabs": "^0.14.0", "express": "^4.19.2", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.24", "luxon": "^3.5.0", "mammoth": "^1.9.0", "modesl": "^0.0.4", "moment": "^2.30.1", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "nodemon": "^3.1.4", "openai": "^4.96.2", "pdf-parse": "^1.1.1", "stream": "^0.0.3", "wav": "^1.0.2", "websocket": "^1.0.35", "xml2js": "^0.6.2"}, "devDependencies": {"@types/morgan": "^1.9.9", "events": "^3.3.0", "stream-browserify": "^3.0.0", "util": "^0.12.5", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}}
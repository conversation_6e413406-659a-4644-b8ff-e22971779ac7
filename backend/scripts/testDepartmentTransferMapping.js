require('dotenv').config();
const mongoose = require('mongoose');
const IVRAddress = require('../models/IVRAddress');
const { generateDepartmentTransferMapping } = require('../utils/prompts');
const { logger } = require('../utils');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  logger.info('MongoDB connected');
  
  try {
    // Get all IVR addresses
    const ivrAddresses = await IVRAddress.find();
    
    // Generate the Department Transfer Mapping prompt
    const departmentTransferMapping = generateDepartmentTransferMapping(ivrAddresses);
    
    // Log the result
    console.log('Department Transfer Mapping:');
    console.log(departmentTransferMapping);
    
    // Test with empty array
    const emptyMapping = generateDepartmentTransferMapping([]);
    console.log('\nEmpty Department Transfer Mapping:');
    console.log(emptyMapping);
    
    // Test with sample data
    const sampleAddresses = [
      {
        phoneNumber: '1234567890',
        description: 'Sales: Product inquiries and orders',
        active: true
      },
      {
        phoneNumber: '0987654321',
        description: 'Support: Technical assistance',
        active: true
      },
      {
        phoneNumber: '5555555555',
        description: 'Billing: Payment and invoice questions',
        active: false // Inactive
      }
    ];
    
    const sampleMapping = generateDepartmentTransferMapping(sampleAddresses);
    console.log('\nSample Department Transfer Mapping:');
    console.log(sampleMapping);
    
  } catch (error) {
    logger.error('Error:', error);
  } finally {
    // Disconnect from MongoDB
    mongoose.disconnect();
    logger.info('MongoDB disconnected');
  }
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

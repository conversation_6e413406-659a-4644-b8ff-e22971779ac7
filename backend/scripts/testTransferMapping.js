require('dotenv').config();
const mongoose = require('mongoose');
const { logger } = require('../utils');
const IVRAddress = require('../models/IVRAddress');
const { generateDepartmentTransferMapping, transfer_call_function_description } = require('../utils/prompts');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  logger.info('MongoDB connected');
  
  try {
    // Get all IVR addresses
    const ivrAddresses = await IVRAddress.find();
    
    // Generate the Department Transfer Mapping prompt
    const departmentTransferMapping = generateDepartmentTransferMapping(ivrAddresses);
    
    // Log the result
    console.log('Department Transfer Mapping:');
    console.log('='.repeat(80));
    console.log(departmentTransferMapping);
    console.log('='.repeat(80));
    
    // Log the transfer call function description
    console.log('\nTransfer Call Function Description:');
    console.log('='.repeat(80));
    console.log(transfer_call_function_description);
    console.log('='.repeat(80));
    
    // Test with sample data
    const sampleAddresses = [
      {
        phoneNumber: '1234',
        description: 'Sales: Product inquiries and orders',
        active: true
      },
      {
        phoneNumber: '5678',
        description: 'Support: Technical assistance',
        active: true
      },
      {
        phoneNumber: '9012',
        description: 'Billing: Payment and invoice questions',
        active: false // Inactive
      }
    ];
    
    const sampleMapping = generateDepartmentTransferMapping(sampleAddresses);
    console.log('\nSample Department Transfer Mapping:');
    console.log('='.repeat(80));
    console.log(sampleMapping);
    console.log('='.repeat(80));
    
  } catch (error) {
    logger.error('Error:', error);
  } finally {
    // Disconnect from MongoDB
    mongoose.disconnect();
    logger.info('MongoDB disconnected');
  }
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

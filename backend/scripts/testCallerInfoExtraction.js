require('dotenv').config();
const OpenAI = require('openai');
const { logger } = require('../utils');

// Initialize OpenAI client
const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
});

/**
 * Extract caller information and generate a call summary using OpenAI
 * @param {string} transcript - The call transcript
 * @returns {Promise<Object>} - Object containing caller information and call summary
 */
async function extractCallerInfoAndSummary(transcript) {
    try {
        // System prompt for extracting caller information
        const systemPrompt = `
You are an AI assistant tasked with analyzing call transcripts.
Extract the following information from the transcript:
1. Caller's first name
2. Caller's last name
3. A concise summary of the call (max 2-3 sentences)

Format your response as a JSON object with the following structure:
{
  "firstName": "string or null if not found",
  "lastName": "string or null if not found",
  "callSummary": "string summarizing the key points of the call"
}

If you cannot determine the first or last name, use null for that field.
Do not make up information that is not in the transcript.
`;

        // Call OpenAI API to extract information
        const response = await openai.chat.completions.create({
            model: "gpt-4-turbo",
            messages: [
                { role: "system", content: systemPrompt },
                { role: "user", content: transcript }
            ],
            temperature: 0.3,
            max_tokens: 500,
            response_format: { type: "json_object" }
        });

        // Parse the response
        const result = JSON.parse(response.choices[0].message.content);
        
        console.log(`Extracted caller info: ${JSON.stringify(result, null, 2)}`);
        
        return result;
    } catch (error) {
        console.error(`Error extracting caller information: ${error.message}`);
        // Return default values if extraction fails
        return {
            firstName: null,
            lastName: null,
            callSummary: "Failed to generate call summary."
        };
    }
}

// Sample transcript
const sampleTranscript = `
assistant: Thank you for calling Total Office National — Melbourne's trusted partner for Office Supplies, Furniture, and Workwear since 1992. How can I assist you today?
user: Hi, my name is John Smith. I'm calling to inquire about your office chair options.
assistant: Hello John, it's nice to meet you. We have a wide range of office chairs available, from ergonomic task chairs to executive leather chairs. Are you looking for something specific?
user: I need about 20 chairs for our new office space. We're looking for something comfortable but not too expensive.
assistant: That sounds like a significant order, John. We have several mid-range ergonomic chairs that would be perfect for a new office setup. Would you like me to transfer you to our Sales Team to discuss specific models and pricing options?
user: Yes, that would be great.
assistant: No problem, I'll connect you to our Sales Team now. Is that okay?
user: Yes, please go ahead.
assistant: Great. Just before I transfer you, may I confirm your phone number in case we get disconnected?
user: Sure, it's 0412 345 678.
assistant: Thank you, John. I'll transfer you to our Sales Team now. Please hold for a moment.
`;

// Test the function
async function runTest() {
    try {
        console.log("Testing caller information extraction...");
        await extractCallerInfoAndSummary(sampleTranscript);
        
        // Test with a transcript that doesn't have a clear name
        const noNameTranscript = `
assistant: Thank you for calling Total Office National — Melbourne's trusted partner for Office Supplies, Furniture, and Workwear since 1992. How can I assist you today?
user: Hi, I'm calling about your delivery times.
assistant: I'd be happy to help with information about our delivery times. Orders placed before 3:30pm are delivered next business day within metro Melbourne. Is there anything specific you'd like to know about our delivery service?
user: That's all I needed to know. Thanks.
assistant: You're welcome. Is there anything else I can assist you with today?
user: No, that's all. Goodbye.
assistant: Thank you for calling Total Office National. Have a wonderful day!
`;
        
        console.log("\nTesting with transcript that doesn't have a clear name...");
        await extractCallerInfoAndSummary(noNameTranscript);
        
    } catch (error) {
        console.error("Test failed:", error);
    }
}

runTest();

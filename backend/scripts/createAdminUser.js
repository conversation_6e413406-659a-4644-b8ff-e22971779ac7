const mongoose = require('mongoose');
const dotenv = require('dotenv');
const User = require('../models/User');
const { logger } = require('../utils');

// Load env vars
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const createAdminUser = async () => {
  try {
    // Admin user details
    const adminData = {
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
    };

    // Check if admin already exists
    const adminExists = await User.findOne({ email: adminData.email });

    if (adminExists) {
      logger.info('Admin user already exists');
      process.exit(0);
    }

    // Create admin user
    const admin = await User.create(adminData);

    logger.info(`Admin user created: ${admin.email}`);
    process.exit(0);
  } catch (error) {
    logger.error('Error creating admin user:', error);
    process.exit(1);
  }
};

createAdminUser();

require('dotenv').config();
const mongoose = require('mongoose');
const { logger } = require('../utils');
const IVRAddress = require('../models/IVRAddress');
const { generateDepartmentTransferMapping } = require('../utils/prompts');
const { DateTime } = require('luxon');
const { system_prompt } = require('../utils/prompts');
const { getLanguageFromCode } = require('../utils');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  logger.info('MongoDB connected');
  
  try {
    // Get all IVR addresses
    const ivrAddresses = await IVRAddress.find();
    
    // Generate the Department Transfer Mapping prompt
    const departmentTransferMapping = generateDepartmentTransferMapping(ivrAddresses);
    
    // Build the prompt with system prompt and department transfer mapping
    const londonTime = DateTime.now().setZone('Europe/London');
    const language = 'en';
    const callType = 'inbound';
    
    const prompt = 
`${system_prompt}\n
## Language:\n Start the conversation in ${getLanguageFromCode(language) || "English"}.\n
## Call Type:\nThis call is ${callType} call. So must follow ${callType} call case.\n
## Current Time in London:\n${londonTime.toFormat('yyyy-MM-dd HH:mm:ss ZZZZ')}\n

Sample Agent Prompt Content\n\n
${departmentTransferMapping}\n\n
`;
    
    // Log the result
    console.log('Full Prompt:');
    console.log('='.repeat(80));
    console.log(prompt);
    console.log('='.repeat(80));
    
    // Log just the Department Transfer Mapping part
    console.log('\nDepartment Transfer Mapping:');
    console.log('='.repeat(80));
    console.log(departmentTransferMapping);
    console.log('='.repeat(80));
    
  } catch (error) {
    logger.error('Error:', error);
  } finally {
    // Disconnect from MongoDB
    mongoose.disconnect();
    logger.info('MongoDB disconnected');
  }
})
.catch(err => {
  logger.error('MongoDB connection error:', err);
  process.exit(1);
});

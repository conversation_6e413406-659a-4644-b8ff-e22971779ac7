const Voice = require('../models/Voice');
const { logger } = require('../utils');

// @desc    Get all voices
// @route   GET /api/voices
// @access  Private
exports.getVoices = async (req, res) => {
  try {
    // Get all voices
    const voices = await Voice.find();

    res.status(200).json({
      success: true,
      count: voices.length,
      data: voices,
    });
  } catch (error) {
    logger.error('Error getting voices:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single voice
// @route   GET /api/voices/:id
// @access  Private
exports.getVoice = async (req, res) => {
  try {
    const voice = await Voice.findById(req.params.id);

    if (!voice) {
      return res.status(404).json({
        success: false,
        message: 'Voice not found',
      });
    }

    // No user ownership check needed

    res.status(200).json({
      success: true,
      data: voice,
    });
  } catch (error) {
    logger.error('Error getting voice:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create new voice
// @route   POST /api/voices
// @access  Private
exports.createVoice = async (req, res) => {
  try {
    // If this voice is set to active, deactivate all other voices
    if (req.body.isActive) {
      await Voice.updateMany(
        {},
        { isActive: false }
      );
    }

    const voice = await Voice.create(req.body);

    res.status(201).json({
      success: true,
      data: voice,
    });
  } catch (error) {
    logger.error('Error creating voice:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update voice
// @route   PUT /api/voices/:id
// @access  Private
exports.updateVoice = async (req, res) => {
  try {
    let voice = await Voice.findById(req.params.id);

    if (!voice) {
      return res.status(404).json({
        success: false,
        message: 'Voice not found',
      });
    }

    // No user ownership check needed

    // If this voice is being set to active, deactivate all other voices
    if (req.body.isActive && !voice.isActive) {
      await Voice.updateMany(
        { _id: { $ne: req.params.id } },
        { isActive: false }
      );
    }

    voice = await Voice.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: Date.now() },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: voice,
    });
  } catch (error) {
    logger.error('Error updating voice:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Set voice as active
// @route   PATCH /api/voices/:id/activate
// @access  Private
exports.activateVoice = async (req, res) => {
  try {
    let voice = await Voice.findById(req.params.id);

    if (!voice) {
      return res.status(404).json({
        success: false,
        message: 'Voice not found',
      });
    }

    // No user ownership check needed

    // Deactivate all voices
    await Voice.updateMany(
      {},
      { isActive: false }
    );

    // Activate the selected voice
    voice = await Voice.findByIdAndUpdate(
      req.params.id,
      { isActive: true, updatedAt: Date.now() },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: voice,
    });
  } catch (error) {
    logger.error('Error activating voice:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Set ElevenLabs voice as active directly
// @route   POST /api/voices/elevenlabs/activate
// @access  Private
exports.activateElevenLabsVoice = async (req, res) => {
  try {
    const { voiceId, name, description, previewUrl, settings } = req.body;

    if (!voiceId || !name) {
      return res.status(400).json({
        success: false,
        message: 'Voice ID and name are required',
      });
    }

    // Find if there's already an active voice
    let activeVoice = await Voice.findOne({
      isActive: true
    });

    if (activeVoice) {
      // If the active voice has the same voiceId, just update its settings
      if (activeVoice.voiceId === voiceId) {
        activeVoice = await Voice.findByIdAndUpdate(
          activeVoice._id,
          {
            name,
            description: description || `${name} voice from ElevenLabs`,
            previewUrl,
            settings: settings || {
              stability: 0.75,
              similarity_boost: 0.75
            },
            updatedAt: Date.now()
          },
          {
            new: true,
            runValidators: true,
          }
        );

        return res.status(200).json({
          success: true,
          data: activeVoice,
        });
      }

      // Otherwise, deactivate the current active voice
      await Voice.findByIdAndUpdate(
        activeVoice._id,
        { isActive: false, updatedAt: Date.now() }
      );
    }

    // Create a new active voice
    const newVoice = await Voice.create({
      name,
      voiceId,
      description: description || `${name} voice from ElevenLabs`,
      previewUrl,
      isActive: true,
      settings: settings || {
        stability: 0.75,
        similarity_boost: 0.75
      },

    });

    res.status(201).json({
      success: true,
      data: newVoice,
    });
  } catch (error) {
    logger.error('Error activating ElevenLabs voice:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete voice
// @route   DELETE /api/voices/:id
// @access  Private
exports.deleteVoice = async (req, res) => {
  try {
    const voice = await Voice.findById(req.params.id);

    if (!voice) {
      return res.status(404).json({
        success: false,
        message: 'Voice not found',
      });
    }

    // No user ownership check needed

    await voice.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    logger.error('Error deleting voice:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

const CallHistory = require('../models/CallHistory');
const { logger } = require('../utils');

// @desc    Get all call history entries
// @route   GET /api/call-history
// @access  Private
exports.getCallHistory = async (req, res) => {
  try {
    // Add pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Add filtering options
    const filter = {};

    // Date range filter
    if (req.query.startDate && req.query.endDate) {
      filter.callStartTime = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }

    // Phone number filter
    if (req.query.phoneNumber) {
      filter.phoneNumber = { $regex: req.query.phoneNumber, $options: 'i' };
    }

    // Transfer status filter
    if (req.query.wasTransferred) {
      filter.wasTransferred = req.query.wasTransferred === 'true';
    }

    // Get total count for pagination
    const total = await CallHistory.countDocuments(filter);

    // Get call history entries
    const callHistory = await CallHistory.find(filter)
      .sort({ callStartTime: -1 }) // Sort by most recent first
      .skip(startIndex)
      .limit(limit)
      .populate('ivrAddress', 'phoneNumber description');

    // Get stats
    const stats = await getCallStats();

    res.status(200).json({
      success: true,
      count: callHistory.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      stats,
      data: callHistory,
    });
  } catch (error) {
    logger.error('Error getting call history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get call history stats
// @route   GET /api/call-history/stats
// @access  Private
exports.getCallStats = async (req, res) => {
  try {
    const stats = await getCallStats();

    res.status(200).json({
      success: true,
      data: stats,
    });
  } catch (error) {
    logger.error('Error getting call stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single call history entry
// @route   GET /api/call-history/:id
// @access  Private
exports.getCallHistoryEntry = async (req, res) => {
  try {
    const callHistoryEntry = await CallHistory.findById(req.params.id)
      .populate('ivrAddress', 'phoneNumber description');

    if (!callHistoryEntry) {
      return res.status(404).json({
        success: false,
        message: 'Call history entry not found',
      });
    }

    // No user ownership check needed

    res.status(200).json({
      success: true,
      data: callHistoryEntry,
    });
  } catch (error) {
    logger.error('Error getting call history entry:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create new call history entry
// @route   POST /api/call-history
// @access  Private
exports.createCallHistoryEntry = async (req, res) => {
  try {
    // No user ID needed

    // Calculate duration if callStartTime and callEndTime are provided
    if (req.body.callStartTime && req.body.callEndTime) {
      const startTime = new Date(req.body.callStartTime);
      const endTime = new Date(req.body.callEndTime);
      req.body.duration = Math.round((endTime - startTime) / 1000); // Duration in seconds
    }

    const callHistoryEntry = await CallHistory.create(req.body);

    res.status(201).json({
      success: true,
      data: callHistoryEntry,
    });
  } catch (error) {
    logger.error('Error creating call history entry:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update call history entry
// @route   PUT /api/call-history/:id
// @access  Private
exports.updateCallHistoryEntry = async (req, res) => {
  try {
    let callHistoryEntry = await CallHistory.findById(req.params.id);

    if (!callHistoryEntry) {
      return res.status(404).json({
        success: false,
        message: 'Call history entry not found',
      });
    }

    // No user ownership check needed

    // Calculate duration if callStartTime and callEndTime are provided
    if (req.body.callStartTime && req.body.callEndTime) {
      const startTime = new Date(req.body.callStartTime);
      const endTime = new Date(req.body.callEndTime);
      req.body.duration = Math.round((endTime - startTime) / 1000); // Duration in seconds
    }

    callHistoryEntry = await CallHistory.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: callHistoryEntry,
    });
  } catch (error) {
    logger.error('Error updating call history entry:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete call history entry
// @route   DELETE /api/call-history/:id
// @access  Private
exports.deleteCallHistoryEntry = async (req, res) => {
  try {
    const callHistoryEntry = await CallHistory.findById(req.params.id);

    if (!callHistoryEntry) {
      return res.status(404).json({
        success: false,
        message: 'Call history entry not found',
      });
    }

    // No user ownership check needed

    await callHistoryEntry.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    logger.error('Error deleting call history entry:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// Helper function to get call stats
const getCallStats = async () => {
  // Get total calls
  const totalCalls = await CallHistory.countDocuments();

  // Get transferred calls
  const transferredCalls = await CallHistory.countDocuments({
    wasTransferred: true
  });

  // Get total call duration
  const durationResult = await CallHistory.aggregate([
    { $group: { _id: null, totalDuration: { $sum: '$duration' } } }
  ]);

  const totalDuration = durationResult.length > 0 ? durationResult[0].totalDuration : 0;

  // Get calls in the last 30 days
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentCalls = await CallHistory.countDocuments({
    callStartTime: { $gte: thirtyDaysAgo }
  });

  // Get previous 30 days for comparison
  const sixtyDaysAgo = new Date();
  sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

  const previousPeriodCalls = await CallHistory.countDocuments({
    callStartTime: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo }
  });

  // Calculate change percentage
  let changePercentage = 0;
  if (previousPeriodCalls > 0) {
    changePercentage = Math.round(((recentCalls - previousPeriodCalls) / previousPeriodCalls) * 100);
  }

  return {
    totalCalls,
    transferredCalls,
    totalDuration,
    recentCalls,
    changePercentage: changePercentage > 0 ? `+${changePercentage}%` : `${changePercentage}%`
  };
};

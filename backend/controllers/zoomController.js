const jwt = require('jsonwebtoken');
const axios = require('axios');
const qs = require('qs');
const { logger } = require('../utils');
const ApiKey = require('../models/ApiKey');

/**
 * Generate a JWT token for Zoom API authentication
 * @param {number} expiresIn - Token expiration time in seconds (default: 1 hour)
 * @returns {Promise<string>} - JWT token
 */
async function generateZoomJWT(expiresIn = 3600) {
    try {
        // Get credentials from database
        const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

        if (!apiKeys || !apiKeys.zoomClientId || !apiKeys.zoomClientSecret) {
            throw new Error('Zoom API credentials not found in database');
        }

        const apiKey = apiKeys.zoomClientId;
        const apiSecret = apiKeys.zoomClientSecret;

        // Current time in seconds since epoch
        const now = Math.floor(Date.now() / 1000);

        // Create payload
        const payload = {
            iss: apiKey,
            exp: now + expiresIn
        };

        // Generate token with HS256 algorithm
        const token = jwt.sign(payload, apiSecret, { algorithm: 'HS256' });

        logger.info(`Generated Zoom JWT token with expiration in ${expiresIn} seconds`);

        return token;
    } catch (error) {
        logger.error(`Error generating Zoom JWT: ${error.message}`);
        throw error;
    }
}

/**
 * Get an OAuth access token from Zoom
 * @returns {Promise<string>} - OAuth access token
 */
async function getZoomOAuthToken() {
    try {
        // Get credentials from database
        const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

        if (!apiKeys || !apiKeys.zoomClientId || !apiKeys.zoomClientSecret || !apiKeys.zoomAccountId) {
            throw new Error('Zoom OAuth credentials not found in database');
        }

        const clientId = apiKeys.zoomClientId;
        const clientSecret = apiKeys.zoomClientSecret;
        const accountId = apiKeys.zoomAccountId;

        // Encode credentials for Basic Auth
        const auth = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');

        // Request body
        const data = qs.stringify({
            'grant_type': 'account_credentials',
            'account_id': accountId
        });

        // Make request to Zoom OAuth endpoint
        const response = await axios.post(
            'https://zoom.us/oauth/token',
            data,
            {
                headers: {
                    'Authorization': `Basic ${auth}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            }
        );

        logger.info('Successfully obtained Zoom OAuth token');
        return response.data.access_token;
    } catch (error) {
        logger.error(`Error getting Zoom OAuth token: ${error.message}`);
        if (error.response) {
            logger.error(`Zoom OAuth error: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

/**
 * Create a Zoom meeting using OAuth authentication
 * @param {Object} meetingDetails - Meeting details
 * @param {string} meetingDetails.topic - Meeting topic
 * @param {string} meetingDetails.agenda - Meeting agenda
 * @param {string} meetingDetails.start_time - Meeting start time (format: YYYY-MM-DDThh:mm:ss)
 * @param {number} meetingDetails.duration - Meeting duration in minutes
 * @param {string} userId - Zoom user ID (default: 'me' for the authenticated user)
 * @returns {Promise<Object>} - Created meeting details
 */
async function createZoomMeeting(meetingDetails, userId = 'me') {
    try {
        // Get OAuth token
        const token = await getZoomOAuthToken();
        logger.info(`Using OAuth token for Zoom API: ${token.substring(0, 20)}...`);

        // Get API keys for contact information
        const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });
        const contactEmail = apiKeys?.zoomContactEmail || "";
        const contactName = apiKeys?.zoomContactName || "";

        logger.info(`Using contact information: ${contactName} <${contactEmail}>`);

        // Default meeting settings
        const defaultMeetingDetails = {
            topic: 'New Meeting',
            type: 2, // Scheduled meeting
            start_time: new Date().toISOString(),
            duration: 60, // 60 minutes
            timezone: 'UTC',
            settings: {
                host_video: true,
                participant_video: true,
                join_before_host: true,
                mute_upon_entry: false,
                auto_recording: 'none',
                audio: 'telephony', // Enable telephony
                telephony: {
                    third_party_audio: true // Enable third-party audio conferencing
                },
                alternative_hosts_email_notification: false,
                use_pmi: false,
                approval_type: 0, // Automatically approve
                registrants_email_notification: false,
                meeting_authentication: false,

                // SIP settings
                audio_conference_info: "SIP URI: sip:<EMAIL>",
                enable_phone_sip_uri: true, // Enable SIP URI for phone calls
                global_dial_in_countries: ["US"], // Add more countries as needed

                // Additional settings that might be needed for SIP
                allow_multiple_devices: true,
                alternative_host_update_polls: true,
                close_registration: false,
                contact_email: contactEmail,
                contact_name: contactName,
                encryption_type: "enhanced_encryption",
                focus_mode: false,
                private_meeting: false,
                show_share_button: true
            }
        };

        // Merge default settings with provided details
        const meetingConfig = { ...defaultMeetingDetails, ...meetingDetails };

        logger.info(`Creating Zoom meeting with config: ${JSON.stringify(meetingConfig)}`);

        // Create meeting
        const response = await axios.post(
            `https://api.zoom.us/v2/users/${userId}/meetings`,
            meetingConfig,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        logger.info(`Zoom meeting created successfully with ID: ${response.data.id}`);
        return response.data;
    } catch (error) {
        logger.error(`Error creating Zoom meeting: ${error.message}`);
        if (error.response) {
            logger.error(`Zoom API error: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

/**
 * Get SIP information for a Zoom meeting
 * @param {string} meetingId - The Zoom meeting ID
 * @returns {Promise<Object>} - SIP information for the meeting
 */
async function getZoomMeetingSIPInfo(meetingId) {
    try {
        // Get OAuth token
        const token = await getZoomOAuthToken();
        logger.info(`Using OAuth token for Zoom API: ${token.substring(0, 20)}...`);

        // Get meeting details
        const response = await axios.get(
            `https://api.zoom.us/v2/meetings/${meetingId}`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        const meetingData = response.data;

        // Extract basic meeting information
        const sipInfo = {
            id: meetingData.id,
            topic: meetingData.topic,
            join_url: meetingData.join_url,
            password: meetingData.password,
            settings: meetingData.settings || {}
        };

        // Get invitation information which contains SIP details
        const sipResponse = await axios.get(
            `https://api.zoom.us/v2/meetings/${meetingId}/invitation`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // Extract invitation text
        const invitationText = sipResponse.data.invitation;
        logger.info(`Retrieved invitation for meeting ID: ${meetingId}`);

        // Add the full invitation text for reference
        sipInfo.invitation = invitationText;

        // Extract SIP URI from the "Join by SIP" section
        const sipSection = invitationText.match(/Join by SIP\s*\n([^\n]+)/i);
        if (sipSection && sipSection[1]) {
            // Extract the SIP URI, removing bullet points and whitespace
            const sipUri = sipSection[1].replace(/^[•\s]+/, '').trim();
            sipInfo.sip_uri = sipUri;
            logger.info(`Found SIP URI: ${sipUri}`);
        }

        // Also look for any SIP URI patterns as a fallback
        const sipUriMatches = invitationText.match(/sip:[^@\s]+@[^@\s]+/gi) || [];
        if (sipUriMatches.length > 0) {
            sipInfo.sip_uris = sipUriMatches;
            logger.info(`Found SIP URI patterns: ${sipUriMatches.join(', ')}`);
        }

        // Look for H.323/SIP section
        const h323Section = invitationText.match(/Join by H\.323\s*\n([\s\S]+?)(?=\n\n|\n---|\n$)/);
        if (h323Section && h323Section[1]) {
            sipInfo.h323_info = h323Section[1].trim();
            logger.info(`Found H.323 information in invitation`);
        }

        // Extract dial-in numbers
        const dialInSection = invitationText.match(/One tap mobile.+?(?=\n\n|\n$)/s);
        if (dialInSection) {
            sipInfo.dial_in_section = dialInSection[0].trim();
            logger.info(`Found dial-in section in invitation`);
        }

        // Extract any custom audio conference info
        if (meetingData.settings?.audio_conference_info) {
            sipInfo.audio_conference_info = meetingData.settings.audio_conference_info;
            logger.info(`Found custom audio conference info: ${sipInfo.audio_conference_info}`);
        }

        // Check if SIP is enabled in settings
        sipInfo.sip_enabled = meetingData.settings?.enable_phone_sip_uri || false;

        if (sipInfo.sip_enabled) {
            logger.info(`SIP is enabled for meeting ID: ${meetingId}`);
        } else {
            logger.info(`SIP is not explicitly enabled in settings for meeting ID: ${meetingId}`);
        }

        return sipInfo;
    } catch (error) {
        logger.error(`Error getting SIP information for meeting: ${error.message}`);
        if (error.response) {
            logger.error(`Zoom API error: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

/**
 * Get SIP information for a Zoom meeting using meeting ID and passcode
 * @param {string} meetingId - The Zoom meeting ID
 * @param {string} passcode - The Zoom meeting passcode
 * @returns {Promise<Object>} - SIP information for the meeting
 */
async function getZoomMeetingSIPInfoWithPasscode(meetingId, passcode) {
    try {
        // Get OAuth token
        const token = await getZoomOAuthToken();
        logger.info(`Using OAuth token for Zoom API: ${token.substring(0, 20)}...`);

        // Get meeting details
        const response = await axios.get(
            `https://api.zoom.us/v2/meetings/${meetingId}`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        const meetingData = response.data;

        // Verify passcode if provided
        if (passcode && meetingData.password && passcode !== meetingData.password) {
            throw new Error('Invalid meeting passcode');
        }

        // Extract basic meeting information
        const sipInfo = {
            id: meetingData.id,
            topic: meetingData.topic,
            join_url: meetingData.join_url,
            password: meetingData.password,
            settings: meetingData.settings || {}
        };

        // Get invitation information which contains SIP details
        const sipResponse = await axios.get(
            `https://api.zoom.us/v2/meetings/${meetingId}/invitation`,
            {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            }
        );

        // Extract invitation text
        const invitationText = sipResponse.data.invitation;
        logger.info(`Retrieved invitation for meeting ID: ${meetingId}`);

        // Add the full invitation text for reference
        sipInfo.invitation = invitationText;

        // Extract SIP URI from the "Join by SIP" section
        const sipSection = invitationText.match(/Join by SIP\s*\n([^\n]+)/i);
        if (sipSection && sipSection[1]) {
            // Extract the SIP URI, removing bullet points and whitespace
            const sipUri = sipSection[1].replace(/^[•\s]+/, '').trim();
            sipInfo.sip_uri = sipUri;
            logger.info(`Found SIP URI: ${sipUri}`);
        }

        // Also look for any SIP URI patterns as a fallback
        const sipUriMatches = invitationText.match(/sip:[^@\s]+@[^@\s]+/gi) || [];
        if (sipUriMatches.length > 0) {
            sipInfo.sip_uris = sipUriMatches;
            logger.info(`Found SIP URI patterns: ${sipUriMatches.join(', ')}`);
        }

        // Check if SIP is enabled in settings
        sipInfo.sip_enabled = meetingData.settings?.enable_phone_sip_uri || false;

        if (sipInfo.sip_enabled) {
            logger.info(`SIP is enabled for meeting ID: ${meetingId}`);
        } else {
            logger.info(`SIP is not explicitly enabled in settings for meeting ID: ${meetingId}`);
        }

        return sipInfo;
    } catch (error) {
        logger.error(`Error getting SIP information for meeting: ${error.message}`);
        if (error.response) {
            logger.error(`Zoom API error: ${JSON.stringify(error.response.data)}`);
        }
        throw error;
    }
}

module.exports = {
    generateZoomJWT,
    getZoomOAuthToken,
    createZoomMeeting,
    getZoomMeetingSIPInfo,
    getZoomMeetingSIPInfoWithPasscode
};
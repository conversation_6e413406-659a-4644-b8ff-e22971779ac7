const fs = require('fs');
const path = require('path');
const { logger } = require('../utils');
const CallHistory = require('../models/CallHistory');

// Directory where recordings are stored
const recordingsDir = path.join('/zoom_bot', 'records');

// @desc    Get all recordings
// @route   GET /api/recordings
// @access  Private
exports.getRecordings = async (req, res) => {
  try {
    // Add pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Get call history entries with completed status
    const filter = { status: 'completed' };

    // Date range filter
    if (req.query.startDate && req.query.endDate) {
      filter.callStartTime = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }

    // Phone number filter
    if (req.query.phoneNumber) {
      filter.phoneNumber = { $regex: req.query.phoneNumber, $options: 'i' };
    }

    // Get total count for pagination
    const total = await CallHistory.countDocuments(filter);

    // Get call history entries
    const callHistory = await CallHistory.find(filter)
      .sort({ callStartTime: -1 }) // Sort by most recent first
      .skip(startIndex)
      .limit(limit)
      .select('_id phoneNumber duration formattedDuration callStartTime callEndTime');

    // Check which recordings actually exist on disk
    const recordings = await Promise.all(
      callHistory.map(async (call) => {
        const recordingPath = path.join(recordingsDir, `${call._id}.wav`);
        const exists = fs.existsSync(recordingPath);

        if (exists) {
          // Get file stats
          const stats = fs.statSync(recordingPath);

          return {
            id: call._id,
            phoneNumber: call.phoneNumber,
            duration: call.duration,
            formattedDuration: call.formattedDuration,
            callStartTime: call.callStartTime,
            callEndTime: call.callEndTime,
            fileSize: stats.size,
            downloadUrl: `/api/recordings/${call._id}/download`,
            streamUrl: `/records/${call._id}.wav`
          };
        }
        return null;
      })
    );

    // Filter out null values (recordings that don't exist)
    const availableRecordings = recordings.filter(recording => recording !== null);

    res.status(200).json({
      success: true,
      count: availableRecordings.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      data: availableRecordings,
    });
  } catch (error) {
    logger.error('Error getting recordings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get recording info
// @route   GET /api/recordings/:id
// @access  Private
exports.getRecording = async (req, res) => {
  try {
    const callId = req.params.id;

    // Check if call history exists
    const callHistory = await CallHistory.findById(callId);

    if (!callHistory) {
      return res.status(404).json({
        success: false,
        message: 'Call history not found',
      });
    }

    // Check if recording file exists
    const recordingPath = path.join(recordingsDir, `${callId}.wav`);

    if (!fs.existsSync(recordingPath)) {
      return res.status(404).json({
        success: false,
        message: 'Recording file not found',
      });
    }

    // Get file stats
    const stats = fs.statSync(recordingPath);

    res.status(200).json({
      success: true,
      data: {
        id: callHistory._id,
        phoneNumber: callHistory.phoneNumber,
        duration: callHistory.duration,
        formattedDuration: callHistory.formattedDuration,
        callStartTime: callHistory.callStartTime,
        callEndTime: callHistory.callEndTime,
        transcript: callHistory.transcript,
        fileSize: stats.size,
        downloadUrl: `/api/recordings/${callId}/download`,
        streamUrl: `/records/${callId}.wav`
      },
    });
  } catch (error) {
    logger.error('Error getting recording info:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Download recording
// @route   GET /api/recordings/:id/download
// @access  Private
exports.downloadRecording = async (req, res) => {
  try {
    const callId = req.params.id;

    // Check if call history exists
    const callHistory = await CallHistory.findById(callId);

    if (!callHistory) {
      return res.status(404).json({
        success: false,
        message: 'Call history not found',
      });
    }

    // Check if recording file exists
    const recordingPath = path.join(recordingsDir, `${callId}.wav`);

    if (!fs.existsSync(recordingPath)) {
      return res.status(404).json({
        success: false,
        message: 'Recording file not found',
      });
    }

    // Format date for filename
    const callDate = new Date(callHistory.callStartTime);
    const formattedDate = callDate.toISOString().split('T')[0];

    // Create a user-friendly filename
    const sanitizedPhoneNumber = callHistory.phoneNumber.replace(/\D/g, '');
    const filename = `Call_${formattedDate}_${sanitizedPhoneNumber}.wav`;

    // Get file stats
    const stats = fs.statSync(recordingPath);

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Accept-Ranges', 'bytes');

    // Stream the file
    const fileStream = fs.createReadStream(recordingPath);
    fileStream.pipe(res);

    // Log the download
    logger.log(`Recording downloaded: ${callId} (${filename})`);
  } catch (error) {
    logger.error('Error downloading recording:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Stream recording for playback
// @route   GET /api/recordings/:id/stream
// @access  Private
exports.streamRecording = async (req, res) => {
  try {
    const callId = req.params.id;

    // Check if call history exists
    const callHistory = await CallHistory.findById(callId);

    if (!callHistory) {
      return res.status(404).json({
        success: false,
        message: 'Call history not found',
      });
    }

    // Check if recording file exists
    const recordingPath = path.join(recordingsDir, `${callId}.wav`);

    if (!fs.existsSync(recordingPath)) {
      return res.status(404).json({
        success: false,
        message: 'Recording file not found',
      });
    }

    // Get file stats
    const stats = fs.statSync(recordingPath);

    // Set headers for streaming
    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Accept-Ranges', 'bytes');
    res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 1 day

    // Stream the file
    const fileStream = fs.createReadStream(recordingPath);
    fileStream.pipe(res);

    // Log the streaming
    logger.log(`Recording streamed: ${callId}`);
  } catch (error) {
    logger.error('Error streaming recording:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

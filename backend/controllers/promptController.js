const Prompt = require('../models/Prompt');
const { logger } = require('../utils');

// @desc    Get all prompts
// @route   GET /api/prompts
// @access  Private
exports.getPrompts = async (req, res) => {
  try {
    // Get all prompts
    const prompts = await Prompt.find();

    res.status(200).json({
      success: true,
      count: prompts.length,
      data: prompts,
    });
  } catch (error) {
    logger.error('Error getting prompts:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get prompts by type
// @route   GET /api/prompts/type/:type
// @access  Private
exports.getPromptsByType = async (req, res) => {
  try {
    const { type } = req.params;

    // Validate type
    if (!['system', 'agent', 'functional'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid prompt type',
      });
    }

    // Get prompts by type for the logged-in user
    const prompts = await Prompt.find({
      user: req.user.id,
      type
    });

    res.status(200).json({
      success: true,
      count: prompts.length,
      data: prompts,
    });
  } catch (error) {
    logger.error('Error getting prompts by type:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get active prompts
// @route   GET /api/prompts/active
// @access  Private
exports.getActivePrompts = async (req, res) => {
  try {
    // Get one active prompt for system and agent types
    const systemPrompt = await Prompt.findOne({
      type: 'system',
      isActive: true
    }).sort({ updatedAt: -1 });

    const agentPrompt = await Prompt.findOne({
      type: 'agent',
      isActive: true
    }).sort({ updatedAt: -1 });

    // Get all active functional prompts
    const functionalPrompts = await Prompt.find({
      type: 'functional',
      isActive: true
    }).sort({ updatedAt: -1 });

    res.status(200).json({
      success: true,
      data: {
        system: systemPrompt,
        agent: agentPrompt,
        functional: functionalPrompts
      },
    });
  } catch (error) {
    logger.error('Error getting active prompts:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Get single prompt
// @route   GET /api/prompts/:id
// @access  Private
exports.getPrompt = async (req, res) => {
  try {
    const prompt = await Prompt.findById(req.params.id);

    if (!prompt) {
      return res.status(404).json({
        success: false,
        message: 'Prompt not found',
      });
    }

    // No user ownership check needed

    res.status(200).json({
      success: true,
      data: prompt,
    });
  } catch (error) {
    logger.error('Error getting prompt:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Create new prompt
// @route   POST /api/prompts
// @access  Private
exports.createPrompt = async (req, res) => {
  try {
    // No user ID needed

    // For system and agent prompts, check if one already exists
    if (req.body.type === 'system' || req.body.type === 'agent') {
      const existingPrompt = await Prompt.findOne({
        type: req.body.type
      });

      if (existingPrompt) {
        return res.status(400).json({
          success: false,
          message: `Only one ${req.body.type} prompt can exist`,
        });
      }
    }

    const prompt = await Prompt.create(req.body);

    res.status(201).json({
      success: true,
      data: prompt,
    });
  } catch (error) {
    logger.error('Error creating prompt:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Update prompt
// @route   PUT /api/prompts/:id
// @access  Private
exports.updatePrompt = async (req, res) => {
  try {
    let prompt = await Prompt.findById(req.params.id);

    if (!prompt) {
      return res.status(404).json({
        success: false,
        message: 'Prompt not found',
      });
    }

    // No user ownership check needed

    prompt = await Prompt.findByIdAndUpdate(
      req.params.id,
      { ...req.body, updatedAt: Date.now() },
      {
        new: true,
        runValidators: true,
      }
    );

    res.status(200).json({
      success: true,
      data: prompt,
    });
  } catch (error) {
    logger.error('Error updating prompt:', error);

    if (error.name === 'ValidationError') {
      const messages = Object.values(error.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(', '),
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Set prompt as active
// @route   PATCH /api/prompts/:id/activate
// @access  Private
exports.activatePrompt = async (req, res) => {
  try {
    let prompt = await Prompt.findById(req.params.id);

    if (!prompt) {
      return res.status(404).json({
        success: false,
        message: 'Prompt not found',
      });
    }

    // No user ownership check needed

    // For functional prompts, toggle the active state
    if (prompt.type === 'functional' && prompt.isActive) {
      // Deactivate the prompt
      prompt = await Prompt.findByIdAndUpdate(
        req.params.id,
        { isActive: false, updatedAt: Date.now() },
        {
          new: true,
          runValidators: true,
        }
      );
    } else {
      // For system and agent prompts, only one can be active at a time
      if (prompt.type === 'system' || prompt.type === 'agent') {
        // Deactivate all prompts of the same type
        await Prompt.updateMany(
          { type: prompt.type },
          { isActive: false }
        );
      }

      // Activate the selected prompt
      prompt = await Prompt.findByIdAndUpdate(
        req.params.id,
        { isActive: true, updatedAt: Date.now() },
        {
          new: true,
          runValidators: true,
        }
      );
    }

    res.status(200).json({
      success: true,
      data: prompt,
    });
  } catch (error) {
    logger.error('Error activating prompt:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

// @desc    Delete prompt
// @route   DELETE /api/prompts/:id
// @access  Private
exports.deletePrompt = async (req, res) => {
  try {
    const prompt = await Prompt.findById(req.params.id);

    if (!prompt) {
      return res.status(404).json({
        success: false,
        message: 'Prompt not found',
      });
    }

    // No user ownership check needed

    // Don't allow deleting active prompts
    if (prompt.isActive) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete an active prompt. Please activate another prompt first.',
      });
    }

    await prompt.deleteOne();

    res.status(200).json({
      success: true,
      data: {},
    });
  } catch (error) {
    logger.error('Error deleting prompt:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
};

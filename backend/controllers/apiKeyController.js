const ApiKey = require('../models/ApiKey');
const { logger } = require('../utils');

// @desc    Get API keys for the current user
// @route   GET /api/settings/api-keys
// @access  Private
exports.getApiKeys = async (req, res) => {
  try {
    let apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      // If no API keys exist for this user, return empty values
      return res.status(200).json({
        success: true,
        data: {
          openaiApiKey: '',
          deepgramApiKey: '',
          deepgramLanguage: 'en',
          deepgramUtterance: 2000,
          deepgramInterruption: 500,
          elevenlabsApiKey: '',
          elevenlabsStability: 0.5,
          elevenlabsSimilarity: 0.8,
          elevenlabsExaggeration: 0.0,
          elevenlabsModel: 'eleven_multilingual_v2',
          openaiModel: 'gpt-3.5-turbo',
          zoomClientId: '',
          zoomClientSecret: '',
          zoomAccountId: '',
          zoomContactEmail: '',
          zoomContactName: ''
        }
      });
    }

    // Mask API keys for security (only show last 4 characters)
    const maskedData = {
      openaiApiKey: maskApiKey(apiKeys.openaiApiKey),
      deepgramApiKey: maskApiKey(apiKeys.deepgramApiKey),
      deepgramLanguage: apiKeys.deepgramLanguage || 'en',
      deepgramUtterance: apiKeys.deepgramUtterance || 2000,
      deepgramInterruption: apiKeys.deepgramInterruption || 500,
      elevenlabsApiKey: maskApiKey(apiKeys.elevenlabsApiKey),
      elevenlabsStability: apiKeys.elevenlabsStability || 0.5,
      elevenlabsSimilarity: apiKeys.elevenlabsSimilarity || 0.8,
      elevenlabsExaggeration: apiKeys.elevenlabsExaggeration || 0.0,
      elevenlabsModel: apiKeys.elevenlabsModel || 'eleven_multilingual_v2',
      openaiModel: apiKeys.openaiModel,
      zoomClientId: maskApiKey(apiKeys.zoomClientId),
      zoomClientSecret: maskApiKey(apiKeys.zoomClientSecret),
      zoomAccountId: maskApiKey(apiKeys.zoomAccountId),
      zoomContactEmail: apiKeys.zoomContactEmail || '',
      zoomContactName: apiKeys.zoomContactName || ''
    };

    res.status(200).json({
      success: true,
      data: maskedData
    });
  } catch (error) {
    logger.error('Error getting API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update API keys for the current user
// @route   PUT /api/settings/api-keys
// @access  Private
exports.updateApiKeys = async (req, res) => {
  try {
    const {
      openaiApiKey, deepgramApiKey, deepgramLanguage, deepgramUtterance, deepgramInterruption,
      elevenlabsApiKey, elevenlabsStability, elevenlabsSimilarity, elevenlabsExaggeration, elevenlabsModel,
      openaiModel, zoomClientId, zoomClientSecret, zoomAccountId, zoomContactEmail, zoomContactName
    } = req.body;

    // Find existing API keys or create new ones
    let apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      apiKeys = new ApiKey();
    }

    // Only update fields that are provided (not empty)
    if (openaiApiKey !== undefined && openaiApiKey !== null) {
      // If the key starts with "sk-" or is not masked, update it
      if (openaiApiKey === '' || !openaiApiKey.includes('*')) {
        apiKeys.openaiApiKey = openaiApiKey;
      }
    }

    if (deepgramApiKey !== undefined && deepgramApiKey !== null) {
      if (deepgramApiKey === '' || !deepgramApiKey.includes('*')) {
        apiKeys.deepgramApiKey = deepgramApiKey;
      }
    }

    if (elevenlabsApiKey !== undefined && elevenlabsApiKey !== null) {
      if (elevenlabsApiKey === '' || !elevenlabsApiKey.includes('*')) {
        apiKeys.elevenlabsApiKey = elevenlabsApiKey;
      }
    }

    if (openaiModel) {
      apiKeys.openaiModel = openaiModel;
    }

    if (elevenlabsModel) {
      apiKeys.elevenlabsModel = elevenlabsModel;
    }

    // Update Deepgram configuration fields
    if (deepgramLanguage) {
      apiKeys.deepgramLanguage = deepgramLanguage;
    }

    if (deepgramUtterance !== undefined && deepgramUtterance !== null) {
      // Ensure the value is within the allowed range
      const utterance = parseInt(deepgramUtterance);
      if (!isNaN(utterance) && utterance >= 1001 && utterance <= 5000) {
        apiKeys.deepgramUtterance = utterance;
      }
    }

    if (deepgramInterruption !== undefined && deepgramInterruption !== null) {
      // Ensure the value is within the allowed range
      const interruption = parseInt(deepgramInterruption);
      if (!isNaN(interruption) && interruption >= 100 && interruption <= 1000) {
        apiKeys.deepgramInterruption = interruption;
      }
    }

    // Update ElevenLabs settings
    if (elevenlabsStability !== undefined && elevenlabsStability !== null) {
      // Ensure the value is within the allowed range
      const stability = parseFloat(elevenlabsStability);
      if (!isNaN(stability) && stability >= 0 && stability <= 1) {
        apiKeys.elevenlabsStability = stability;
      }
    }

    if (elevenlabsSimilarity !== undefined && elevenlabsSimilarity !== null) {
      // Ensure the value is within the allowed range
      const similarity = parseFloat(elevenlabsSimilarity);
      if (!isNaN(similarity) && similarity >= 0 && similarity <= 1) {
        apiKeys.elevenlabsSimilarity = similarity;
      }
    }

    if (elevenlabsExaggeration !== undefined && elevenlabsExaggeration !== null) {
      // Ensure the value is within the allowed range
      const exaggeration = parseFloat(elevenlabsExaggeration);
      if (!isNaN(exaggeration) && exaggeration >= 0 && exaggeration <= 1) {
        apiKeys.elevenlabsExaggeration = exaggeration;
      }
    }

    // Update Zoom API keys
    if (zoomClientId !== undefined && zoomClientId !== null) {
      if (zoomClientId === '' || !zoomClientId.includes('*')) {
        apiKeys.zoomClientId = zoomClientId;
      }
    }

    if (zoomClientSecret !== undefined && zoomClientSecret !== null) {
      if (zoomClientSecret === '' || !zoomClientSecret.includes('*')) {
        apiKeys.zoomClientSecret = zoomClientSecret;
      }
    }

    if (zoomAccountId !== undefined && zoomAccountId !== null) {
      if (zoomAccountId === '' || !zoomAccountId.includes('*')) {
        apiKeys.zoomAccountId = zoomAccountId;
      }
    }

    if (zoomContactEmail !== undefined) {
      apiKeys.zoomContactEmail = zoomContactEmail;
    }

    if (zoomContactName !== undefined) {
      apiKeys.zoomContactName = zoomContactName;
    }

    await apiKeys.save();

    // Mask API keys for the response
    const maskedData = {
      openaiApiKey: maskApiKey(apiKeys.openaiApiKey),
      deepgramApiKey: maskApiKey(apiKeys.deepgramApiKey),
      deepgramLanguage: apiKeys.deepgramLanguage,
      deepgramUtterance: apiKeys.deepgramUtterance,
      deepgramInterruption: apiKeys.deepgramInterruption,
      elevenlabsApiKey: maskApiKey(apiKeys.elevenlabsApiKey),
      elevenlabsStability: apiKeys.elevenlabsStability,
      elevenlabsSimilarity: apiKeys.elevenlabsSimilarity,
      elevenlabsExaggeration: apiKeys.elevenlabsExaggeration,
      elevenlabsModel: apiKeys.elevenlabsModel,
      openaiModel: apiKeys.openaiModel,
      zoomClientId: maskApiKey(apiKeys.zoomClientId),
      zoomClientSecret: maskApiKey(apiKeys.zoomClientSecret),
      zoomAccountId: maskApiKey(apiKeys.zoomAccountId),
      zoomContactEmail: apiKeys.zoomContactEmail,
      zoomContactName: apiKeys.zoomContactName
    };

    res.status(200).json({
      success: true,
      data: maskedData
    });
  } catch (error) {
    logger.error('Error updating API keys:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Validate OpenAI API key
// @route   POST /api/settings/api-keys/validate/openai
// @access  Private
exports.validateOpenAIKey = async (req, res) => {
  try {
    const { apiKey } = req.body;

    // Simple validation - check if key starts with "sk-"
    if (!apiKey || !apiKey.startsWith('sk-')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid API key format'
      });
    }

    // Try to make a simple API call to OpenAI
    // Import OpenAI - the new version has a different import pattern
    const OpenAI = require("openai");

    try {
      // Create a new OpenAI client with the API key
      const openai = new OpenAI({
        apiKey: apiKey
      });

      // Make a simple models list request to validate the key
      await openai.models.list();

      // If validation is successful, save the API key to the user's record
      let apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

      if (!apiKeys) {
        // Create a new record if one doesn't exist
        apiKeys = new ApiKey();
      }

      // Update the OpenAI API key
      apiKeys.openaiApiKey = apiKey;
      await apiKeys.save();

      // Return success response with masked API key
      res.status(200).json({
        success: true,
        message: 'API key is valid and has been saved',
        data: {
          openaiApiKey: maskApiKey(apiKey)
        }
      });
    } catch (apiError) {
      logger.error('OpenAI API validation error:', apiError);
      res.status(400).json({
        success: false,
        message: 'Invalid API key or API error'
      });
    }
  } catch (error) {
    logger.error('Error validating OpenAI API key:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// Helper function to mask API keys
function maskApiKey(apiKey) {
  if (!apiKey) return '';
  if (apiKey.length <= 8) return '********';

  // Show only the last 4 characters
  return '********' + apiKey.slice(-4);
}

// @desc    Get available OpenAI models
// @route   GET /api/settings/api-keys/openai-models
// @access  Private
exports.getOpenAIModels = async (req, res) => {
  try {
    // Find the API key (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys || !apiKeys.openaiApiKey) {
      return res.status(400).json({
        success: false,
        message: 'OpenAI API key not found'
      });
    }

    // Get models from OpenAI using the new API format
    const OpenAI = require("openai");

    try {
      // Create a new OpenAI client with the API key
      const openai = new OpenAI({
        apiKey: apiKeys.openaiApiKey
      });

      // Get the list of models
      const response = await openai.models.list();

      // Filter for chat models only
      const chatModels = response.data
        .filter(model =>
          model.id.includes('gpt') &&
          !model.id.includes('instruct') &&
          !model.id.includes('-vision-')
        )
        .map(model => ({
          id: model.id,
          name: model.id
        }))
        .sort((a, b) => a.name.localeCompare(b.name));

      res.status(200).json({
        success: true,
        data: chatModels
      });
    } catch (apiError) {
      logger.error('OpenAI API error:', apiError);
      res.status(400).json({
        success: false,
        message: 'Error fetching models: ' + (apiError.response?.data?.error?.message || apiError.message || 'API error')
      });
    }
  } catch (error) {
    logger.error('Error getting OpenAI models:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

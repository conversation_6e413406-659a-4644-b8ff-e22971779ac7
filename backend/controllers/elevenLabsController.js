const ApiKey = require('../models/ApiKey');
const { logger } = require('../utils');
const { ElevenLabsClient } = require('elevenlabs');

// @desc    Get voices from ElevenLabs
// @route   GET /api/elevenlabs/voices
// @access  Private
exports.getVoices = async (req, res) => {
  try {
    // Find the API key (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys || !apiKeys.elevenlabsApiKey) {
      return res.status(400).json({
        success: false,
        message: 'ElevenLabs API key not found. Please add your API key in settings.'
      });
    }

    // Create ElevenLabs client with the API key
    const client = new ElevenLabsClient({
      apiKey: apiKeys.elevenlabsApiKey
    });

    // Fetch voices from ElevenLabs
    const response = await client.voices.getAll({
      include_total_count: true
    });

    res.status(200).json({
      success: true,
      data: response
    });
  } catch (error) {
    logger.error('Error fetching ElevenLabs voices:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

// @desc    Get active voice settings
// @route   GET /api/elevenlabs/settings
// @access  Private
exports.getSettings = async (req, res) => {
  try {
    // Find the API key (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys) {
      return res.status(200).json({
        success: true,
        data: {
          stability: 0.5,
          similarity: 0.8,
          exaggeration: 0.0
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        stability: apiKeys.elevenlabsStability || 0.5,
        similarity: apiKeys.elevenlabsSimilarity || 0.8,
        exaggeration: apiKeys.elevenlabsExaggeration || 0.0
      }
    });
  } catch (error) {
    logger.error('Error getting ElevenLabs settings:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get models from ElevenLabs
// @route   GET /api/elevenlabs/models
// @access  Private
exports.getModels = async (req, res) => {
  try {
    // Find the API key (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys || !apiKeys.elevenlabsApiKey) {
      return res.status(400).json({
        success: false,
        message: 'ElevenLabs API key not found. Please add your API key in settings.'
      });
    }

    // Create ElevenLabs client with the API key
    const client = new ElevenLabsClient({
      apiKey: apiKeys.elevenlabsApiKey
    });

    // Fetch models from ElevenLabs
    const models = await client.models.getAll();

    res.status(200).json({
      success: true,
      data: models
    });
  } catch (error) {
    logger.error('Error fetching ElevenLabs models:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

// @desc    Verify a voice ID from ElevenLabs
// @route   GET /api/elevenlabs/voices/:id/verify
// @access  Private
exports.verifyVoiceId = async (req, res) => {
  try {
    const voiceId = req.params.id;

    if (!voiceId) {
      return res.status(400).json({
        success: false,
        message: 'Voice ID is required'
      });
    }

    // Find the API key (system-wide)
    const apiKeys = await ApiKey.findOne().sort({ updatedAt: -1 });

    if (!apiKeys || !apiKeys.elevenlabsApiKey) {
      return res.status(400).json({
        success: false,
        message: 'ElevenLabs API key not found. Please add your API key in settings.'
      });
    }

    // Create ElevenLabs client with the API key
    const client = new ElevenLabsClient({
      apiKey: apiKeys.elevenlabsApiKey
    });

    try {
      // Fetch the specific voice from ElevenLabs
      const voice = await client.voices.get(voiceId);

      // Format the response to match the structure expected by the frontend
      const formattedVoice = {
        voice_id: voice.voice_id,
        name: voice.name,
        description: voice.description,
        preview_url: voice.preview_url,
        labels: voice.labels || {},
        settings: {
          stability: voice.settings?.stability || 0.75,
          similarity_boost: voice.settings?.similarity_boost || 0.75
        }
      };

      res.status(200).json({
        success: true,
        data: formattedVoice
      });
    } catch (error) {
      // If the voice ID is invalid, ElevenLabs will throw an error
      return res.status(404).json({
        success: false,
        message: 'Voice ID not found or invalid'
      });
    }
  } catch (error) {
    logger.error('Error verifying ElevenLabs voice ID:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Server error'
    });
  }
};

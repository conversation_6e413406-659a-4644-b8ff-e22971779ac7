# Authentication System

This project uses MongoDB for user authentication with <PERSON>W<PERSON> (JSON Web Tokens).

## Setup

1. Install MongoDB locally or use MongoDB Atlas
2. Create a `.env` file based on `.env.example`
3. Set the MongoDB connection string in the `.env` file
4. Set JWT secret and expiration settings

## Environment Variables

```
MONGO_URI=mongodb://localhost:27017/zoom_bot
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRE=30d
JWT_COOKIE_EXPIRE=30
```

## Creating an Admin User

Run the following command to create an admin user:

```bash
node scripts/createAdminUser.js
```

This will create an admin user with the following credentials:
- Email: <EMAIL>
- Password: admin123

## API Endpoints

### Authentication

- `POST /auth/signup` - Register a new user
- `POST /auth/signin` - Login a user
- `GET /auth/signout` - Logout a user
- `GET /auth/me` - Get current logged in user (protected route)

## Frontend Integration

The frontend uses the `AuthContext` to manage authentication state. The context provides:

- `login(email, password)` - Login a user
- `signup(email, password, name)` - Register a new user
- `logout()` - Logout a user
- `authState` - Current authentication state

## Protected Routes

To protect routes in the backend, use the `protect` middleware:

```javascript
const { protect } = require('../middleware/auth');

router.get('/protected-route', protect, controllerFunction);
```

To restrict routes to specific roles, use the `authorize` middleware:

```javascript
const { protect, authorize } = require('../middleware/auth');

router.get('/admin-route', protect, authorize('admin'), adminControllerFunction);
```

## JWT Authentication Flow

1. User registers or logs in
2. Server validates credentials and returns a JWT token
3. Frontend stores the token in localStorage
4. Frontend includes the token in the Authorization header for subsequent requests
5. Server validates the token for protected routes
6. When user logs out, the token is removed from localStorage

const { OpenAI } = require('openai');
const dotenv = require("dotenv");
const path = require('path');
const { ElevenLabsClient, ElevenLabs } = require('elevenlabs');
const fs = require("fs");
const { logger, getOpenAISettings } = require('../utils');
const { TOOL_LIST, FUNCTION_MAP } = require('../utils/toolsConfig');
dotenv.config();

// Initialize OpenAI client with global API key (falls back to env variable if global not set)
const openai = new OpenAI({
    apiKey: global.OPENAI_API_KEY || process.env.OPENAI_API_KEY,
  });

// Initialize ElevenLabs client with global API key
const client = new ElevenLabsClient({
    apiKey: global.ELEVENLABS_API_KEY || process.env.ELEVENLABS_API_KEY
});

const zoomBotDir = '/zoom_bot';
if (!fs.existsSync(zoomBotDir)) {
    fs.mkdirSync(zoomBotDir, { recursive: true });
}

// Create tmp directory inside /zoom_bot
const dirPath = path.join(zoomBotDir, 'tmp');
if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
}

function mergeChatHistory(chatHistory) {
    if (!chatHistory || chatHistory.length === 0) return [];

    const mergedHistory = [];
    let currentMessage = chatHistory[0];

    for (let i = 1; i < chatHistory.length; i++) {
        if (chatHistory[i].role === currentMessage.role) {
            // Merge content if the role is the same
            currentMessage.content += ' ' + chatHistory[i].content;
        } else {
            // Push the current message and start a new one
            mergedHistory.push(currentMessage);
            currentMessage = chatHistory[i];
        }
    }
    // Push the last message
    mergedHistory.push(currentMessage);

    return mergedHistory;
}

async function promptLLM(mediaStream, aiTalking) {
    try {
        const systemPrompt = mediaStream.prompt;

        const firstResponseTime = process.hrtime(); // Capture start time
        mediaStream.chatHistory = mergeChatHistory(mediaStream.chatHistory);

        // Get OpenAI model from database
        const openAISettings = await getOpenAISettings();
        const modelName = openAISettings.model || 'gpt-4o-mini'; // Default to gpt-4o-mini if not found

        logger.info(`Using OpenAI model from database: ${modelName}`);

        const responseStream = await openai.chat.completions.create({
            model: modelName,
            messages: [
                { role: 'system', content: systemPrompt },
                ...mediaStream.chatHistory,
            ],
            temperature: mediaStream.temperature,
            tools: TOOL_LIST,
            stream: true, // Enable streaming
        });

        if (aiTalking != mediaStream.aiTalking) return;

        let accumulatedText = '';
        let sentenceEndRegex = /[.!?]/; // .!? should be import here
        let firstSentenceLogged = false;
        let toolCalls = [];

        for await (const chunk of responseStream) {
            const delta = chunk.choices[0].delta;
            const finishReason = chunk.choices[0].finish_reason;

            // Handle text content
            if (aiTalking != mediaStream.aiTalking) return;
            if (delta.content) {
                accumulatedText += delta.content;
                // Check if the accumulated text forms a complete sentence
                if (sentenceEndRegex.test(accumulatedText)) {
                    // Extract the complete sentence
                    if (aiTalking != mediaStream.aiTalking) return;
                    const sentences = accumulatedText.split(sentenceEndRegex);
                    for (let i = 0; i < sentences.length - 1; i++) {
                        const sentence = sentences[i] + accumulatedText.match(sentenceEndRegex)[0];

                        if (!firstSentenceLogged) {
                            // Capture the end time for the first sentence
                            const firstSentenceTime = process.hrtime(firstResponseTime);
                            // Convert the time to seconds
                            const firstSentenceTimeInSeconds = (firstSentenceTime[0] + firstSentenceTime[1] / 1e9).toFixed(4);
                            logger.info(`${mediaStream.logDate()} ${firstSentenceTimeInSeconds}s on openAI`.yellow);
                            firstSentenceLogged = true;
                            mediaStream.agent_start_time = process.hrtime();
                        }
                        if (aiTalking != mediaStream.aiTalking) return;
                        await synthesizeAudio(mediaStream, sentence, aiTalking);
                    }

                    // Keep the last incomplete sentence in accumulatedText
                    accumulatedText = sentences[sentences.length - 1];
                }
            }

            // Handle tool calls
            if (delta.tool_calls) {
                delta.tool_calls.forEach(toolCall => processToolCall(toolCall, toolCalls));
            }

            // Process tool calls when complete
            if (finishReason === 'tool_calls' && toolCalls.length > 0 && toolCalls.every(call => call.arguments && call.function_name)) {
                await handleToolCalls(toolCalls, mediaStream, aiTalking);
                toolCalls = [];
            }

            // Handle completion of text response
            if (finishReason === 'stop' && accumulatedText) {
                // Handle any remaining text that didn't end with a sentence
                if (accumulatedText.trim().length > 0) {
                    await synthesizeAudio(mediaStream, accumulatedText, aiTalking);
                }
            }
        }

    } catch (error) {
        logger.error('Error:', error);
    }
}

processToolCall = (toolCall, toolCalls) => {
    const currentId = toolCall.id;
    if (!toolCalls.includes(currentId) && currentId !== undefined) {
        let temp = {
            index: toolCall.index,
            id: currentId,
            arguments: toolCall.function?.arguments || ""
        };
        if (toolCall.function?.name) {
            temp.function_name = toolCall.function.name;
            // console.log("Function name:", toolCall.function.name);
        }
        toolCalls.push(temp);
    } else {
        // console.log("Tool Call:", toolCall);
        let tool_item = toolCalls.find(call => call.index === toolCall.index);
        tool_item.arguments += toolCall.function?.arguments || "";
    }
}

handleToolCalls = async (toolCalls, mediaStream, aiTalking) => {
    console.log("Processing toolCalls:", toolCalls);
    const toolResults = await Promise.all(toolCalls.map(async (toolCall) => {
        const functionName = toolCall.function_name;
        const args = JSON.parse(toolCall.arguments);
        const toolResult = await FUNCTION_MAP[functionName](args, mediaStream, aiTalking);

        // console.log(`Tool result for ${functionName}:`, toolResult);
        return {
            role: "tool",
            content: JSON.stringify(toolResult),
            tool_call_id: toolCall.id
        };
    }));

    let toolcall_payload = {
        role: "assistant",
        content: null,
        tool_calls: toolCalls.map((toolCall)=> {
            return {
                id: toolCall.id,
                function: {
                    name: toolCall.function_name,
                    arguments: JSON.stringify(toolCall.arguments)
                },
                type: "function"
            }
        })
    };

    mediaStream.chatHistory.push(toolcall_payload);
    mediaStream.chatHistory.push(...toolResults);

    promptLLM(mediaStream, aiTalking);
}


async function synthesizeAudio(mediaStream, text, aiTalking) {
    try {
        if (aiTalking != mediaStream.aiTalking || mediaStream.isConnected == false) return;

        // Use the settings already loaded in mediaStream
        let stability = mediaStream.stability;
        let similarity = mediaStream.similarity;
        let exaggeration = mediaStream.exaggeration;

        const audioStream = await client.textToSpeech.convertAsStream(
            mediaStream.voiceID,
            {
                text: `"${text}"`,
                //previous_text: "You should say slowly and seriously. Don't shout or yell and protect the voice actor",
                model_id: "eleven_turbo_v2_5",
                voice_settings: {
                    "stability": Number(stability),
                    "similarity_boost": Number(similarity),
                    "style": Number(exaggeration),
                    "use_speaker_boost": true
                },
                output_format: ElevenLabs.OutputFormat.Pcm16000,
                optimize_streaming_latency: ElevenLabs.OptimizeStreamingLatency.Three,
            }
        );
        if (aiTalking != mediaStream.aiTalking) return;
        mediaStream.transcriptionService.setAISpeaking(true);
        await mediaStream.handleAudioData(audioStream, text, aiTalking);
    } catch (error) {
        logger.error('Error during TTS synthesis:', error);
    }
}

module.exports = { promptLLM };

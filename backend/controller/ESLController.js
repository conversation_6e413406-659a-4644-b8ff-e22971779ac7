"use strict";
require('colors');
const ESL = require('modesl');
const { logger } = require('../utils');
const fsServer = "127.0.0.1"; // will import from env in future
const eslHost = "127.0.0.1"; // will import from env in future
const eslPort = "8021"; // will import from env in future
const eslPassword = "ClueCon"; // will import from env in future
let esl = null;

class ESLController {
  static async init() {
    esl = new ESL.Connection(eslHost, eslPort, eslPassword, () => {
      esl.subscribe("ALL", () => {
        esl.on("esl::**", (event) => {
          event.fsServer = fsServer;
        });
        logger.log("Subscribed to FreeSWITCH events".gray);
      });
    });

    esl.on("error", async (error) => {
      logger.log("FreeSWITCH ESL Error".red);
      logger.log(error);
      esl = null;
      if (error.code === "ECONNREFUSED") {
        await delay(5000);
        ESLController.init();
      }
    });

    esl.on("esl::ready", () => {
      logger.log("Connected to FreeSWITCH ESL".gray);
    });

    esl.on("esl::end", async () => {
      logger.log("FreeSWITCH ESL connection closed".red);
      esl = null;
      await delay(5000);
      ESLController.init();
    });
  }

  static async event(event) {
    return new Promise((resolve, reject) => {
      if (esl) {
        esl.bgapi(event.command, event.arg, (response) => {
          try {
            if (response && response.body) {
              if (response.body.includes('USER_BUSY')) {
                resolve({ status: 'USER_BUSY' });
              } else if (response.body.includes('INVALID_GATEWAY')) {
                resolve({ status: 'INVALID_GATEWAY' });
              } else if (response.body.includes('INCOMPATIBLE_DESTINATION')) {
                // Handle the specific INCOMPATIBLE_DESTINATION error
                logger.error('Incompatible destination error. This may indicate SIP configuration issues with Zoom.');
                resolve({ status: 'INCOMPATIBLE_DESTINATION', body: response.body });
              } else if (response.body.includes('ERR')) {
                resolve({ status: 'ERROR', body: response.body });
              } else {
                resolve({ status: 'SUCCESS', uuid: response.body });
              }
            } else {
              logger.warn('Unexpected response format:', response);
              resolve({ status: 'UNKNOWN' });
            }
          } catch (error) {
            logger.error('Error processing response:', error);
            reject(error);
          }
        });
      } else {
        reject(new Error('ESL connection not established'.red));
      }
    });
  }

  static async joinZoomMeetingViaSIP(options) {
    try {
      const {
        sipUri, passcode
      } = options;

      if (!sipUri) {
        throw new Error('SIP URI is required to join the Zoom meeting');
      }

      // Format the SIP URI if needed
      const formattedSipUri = sipUri.startsWith('sip:') ? sipUri.substring(4) : sipUri;

      // Validate the SIP URI format
      if (!formattedSipUri.includes('@')) {
        logger.error(`Invalid SIP URI format: ${formattedSipUri}`);
        return { status: 'ERROR', error: 'Invalid SIP URI format' };
      }

      let finalSipUri = formattedSipUri;

      // Extract meeting ID and domain
      const [meetingId, domain] = formattedSipUri.split('@');

      // Log the extracted parts for debugging
      logger.info(`SIP URI parts - Meeting ID: ${meetingId}, Domain: ${domain}`);

      // Build the originate command with additional parameters for better compatibility
      // const originateCommand = `originate {ignore_early_media=true,originate_timeout=60,passcode=${passcode}}sofia/gateway/zoom/sip:${meetingId}.${passcode}@zoomcrc.com &lua(/etc/freeswitch/scripts/product_outbound.lua)`;

      const originateCommand = `originate {ignore_early_media=true,originate_timeout=60,origination_caller_id_number=1000,origination_caller_id_name=1000,rtp_secure_media=true}sofia/gateway/zoom/+${meetingId}@zoomcrc.com &park()`;

      logger.info(`Joining Zoom meeting via SIP: ${finalSipUri}`);
      logger.info(`Originate command: ${originateCommand}`);

      // Execute the originate command
      const result = await this.event({ command: originateCommand });

      if (result.status === 'SUCCESS') {
        logger.info(`Successfully joined Zoom meeting. Call UUID: ${result.uuid}`);
      } else if (result.status === 'INCOMPATIBLE_DESTINATION') {
        logger.error(`Failed to join Zoom meeting due to incompatible destination. This may indicate SIP configuration issues with Zoom.`);
        if (result.body) {
          logger.error(`Error details: ${result.body}`);
        }
      } else {
        logger.error(`Failed to join Zoom meeting: ${result.status}`);
        if (result.body) {
          logger.error(`Error details: ${result.body}`);
        }
      }

      return result;
    } catch (error) {
      logger.error(`Error joining Zoom meeting: ${error.message}`);
      throw error;
    }
  }
}

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

module.exports = ESLController;
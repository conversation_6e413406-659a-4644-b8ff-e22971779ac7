require('colors');
const { createClient, LiveTranscriptionEvents } = require('@deepgram/sdk');
const EventEmitter = require('events');
const { logger } = require('../utils');

class TranscriptionService extends EventEmitter {
  constructor(language, interruption, utterance) {
    super();
    this.dgClient = createClient(global.DEEPGRAM_API_KEY);
    this.dgConnection = this.initializeConnection(language, interruption, utterance);

    this.finalResult = '';
    this.is_finals = [];
    this.aiSpeaking = false;

    this.keepAlive = setInterval(() => {
      this.dgConnection.keepAlive();
    }, 5 * 1000);

    this.setupEventHandlers();
  }

  initializeConnection(language, interruption, utterance) {
    let config = {
      language,
      punctuate: true,
      smart_format: true,
      model: 'nova-2',
      encoding: 'linear16',
      sample_rate: 16000,
      interim_results: true,
      utterance_end_ms: utterance,
      vad_events: true,
      endpointing: interruption,
    };
    return this.dgClient.listen.live(config);
  }

  setupEventHandlers() {
    this.dgConnection.on(LiveTranscriptionEvents.Open, this.onOpen.bind(this));
    this.dgConnection.on(LiveTranscriptionEvents.Transcript, this.onTranscript.bind(this));
    this.dgConnection.on(LiveTranscriptionEvents.UtteranceEnd, this.onUtteranceEnd.bind(this));
    this.dgConnection.on(LiveTranscriptionEvents.Error, this.onError.bind(this));
    this.dgConnection.on(LiveTranscriptionEvents.Metadata, this.onMetadata.bind(this));
    this.dgConnection.on(LiveTranscriptionEvents.Close, this.onClose.bind(this));
  }

  onOpen() {
    logger.log('STT -> Deepgram connection opened'.green);
  }

  onTranscript(transcriptionEvent) {
    const alternatives = transcriptionEvent.channel?.alternatives;
    const transcript = alternatives?.[0]?.transcript || '';

    if (transcriptionEvent.type === 'UtteranceEnd') {
      this.handleUtteranceEnd();
      return;
    }

    if (transcript !== '') {
      this.handleTranscript(transcriptionEvent, transcript);
    }
  }

  handleTranscript(transcriptionEvent, transcript) {
    this.maybeStopAISpeaking(transcript);
    if (transcriptionEvent.is_final) {
      this.is_finals.push(transcript);

      if (transcriptionEvent.speech_final) {
        this.emitFinalTranscription();
      }
    }
  }

  handleUtteranceEnd() {
    if (this.is_finals.length > 0) {
      this.emitFinalTranscription();
    }
  }

  emitFinalTranscription() {
    const utterance = this.is_finals.join(" ");
    this.is_finals = [];
    this.emit('transcription', utterance);
  }

  maybeStopAISpeaking(transcript) {
    if (this.aiSpeaking) {
      const words = transcript.split(/\s+/);
      if (words.length >= 2) {
        this.emit('stopaispeaking');
        this.aiSpeaking = false;
      }
    }
  }

  onUtteranceEnd() {
    if (this.is_finals.length > 0) {
      this.emitFinalTranscription();
    }
  }

  onError(error) {
    logger.error('STT -> Deepgram error', error);
    // Implement retry logic if needed
  }

  onMetadata(metadata) {
  }

  onClose() {
    logger.log('STT -> Deepgram connection closed'.yellow);
    clearInterval(this.keepAlive);
    this.dgConnection.requestClose();
  }

  send(payload) {
    if (this.dgConnection.getReadyState() === 1) {
      this.dgConnection.send(payload);
    }
  }

  setAISpeaking(aiSpeaking) {
    this.aiSpeaking = aiSpeaking;
  }
}

module.exports = { TranscriptionService };
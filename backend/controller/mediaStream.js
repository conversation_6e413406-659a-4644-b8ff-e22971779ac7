const { TranscriptionService } = require('./dgTranscription');
const { promptLLM } = require('./agentLLM');
const ESLController = require("./ESLController");
const fs = require('fs');
const dotenv = require("dotenv");
const { PassThrough, EventEmitter } = require('stream');
const path = require('path');
const { DateTime } = require('luxon');
// ElevenLabs client is now used in agentLLM.js
const mongoose = require('mongoose');
const axios = require('axios');
const OpenAI = require('openai');
const {
    getLanguageFromCode,
    logger,
    getDeepgramSettings,
    getElevenLabsSettings,
    getActiveVoice,
    getAgentPrompt,
    getOpenAISettings
} = require('../utils');
const { system_prompt } = require('../utils/prompts');
const CallHistory = require('../models/CallHistory');
require('colors');

dotenv.config();

// Initialize OpenAI client with global API key (falls back to env variable if global not set)
const openai = new OpenAI({
    apiKey: global.OPENAI_API_KEY || process.env.OPENAI_API_KEY,
});

// Create /zoom_bot directory if it doesn't exist
const zoomBotDir = '/zoom_bot';
if (!fs.existsSync(zoomBotDir)) {
    fs.mkdirSync(zoomBotDir, { recursive: true });
}

// Create tmp directory inside /zoom_bot
const dirPath = path.join(zoomBotDir, 'tmp');
if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
}

function createWavHeader(dataSize, numChannels, sampleRate, bitsPerSample) {
    const header = Buffer.alloc(44);
    header.write('RIFF', 0); // ChunkID
    header.writeUInt32LE(36 + dataSize, 4); // ChunkSize (4 bytes)
    header.write('WAVE', 8); // Format
    header.write('fmt ', 12); // Subchunk1ID
    header.writeUInt32LE(16, 16); // Subchunk1Size (16 for PCM)
    header.writeUInt16LE(1, 20); // AudioFormat (1 for PCM)
    header.writeUInt16LE(numChannels, 22); // NumChannels
    header.writeUInt32LE(sampleRate, 24); // SampleRate
    header.writeUInt32LE(sampleRate * numChannels * bitsPerSample / 8, 28); // ByteRate
    header.writeUInt16LE(numChannels * bitsPerSample / 8, 32); // BlockAlign
    header.writeUInt16LE(bitsPerSample, 34); // BitsPerSample
    header.write('data', 36); // Subchunk2ID
    header.writeUInt32LE(dataSize, 40); // Subchunk2Size

    return header;
}

function pcmToWav(pcmBuffer, numChannels = 1, sampleRate = 16000, bitsPerSample = 16) {
    const wavHeader = createWavHeader(pcmBuffer.length, numChannels, sampleRate, bitsPerSample);
    return Buffer.concat([wavHeader, pcmBuffer]);
}

class MediaStream extends EventEmitter {
    constructor(connection) {
        super();
        this.connection = connection;
        this.isConnected = true;
        this.chatHistory = [];
        this.prompt = "";
        this.transcriptionService = null;
        this.language = "en";
        this.gender = "female";
        this.voiceID = "";
        this.agent_id = 0;
        this.call_id = 0;
        this.agent_start_time = process.hrtime();
        this.callDuration = process.hrtime();
        this.aiTalking = 1;
        this.temperature = 0.5;
        this.interruption = 150; // Default value, will be updated from settings
        this.utterance = 1100; // Default value, will be updated from settings
        this.agent_name = "Anna";
        this.user = {};
        this.callType = "inbound";
        this.isReady = false;
        this.uuid = "";
        this.stability = 0.5;
        this.similarity = 0.8;
        this.exaggeration = 0.0;
        this.destination = "";
        this.agentType = "";
        this.queue = [];
        this.isPlaying = false;
        this.count = 0;
        this.transferredTo = null; // Track if call was transferred

        connection.on("message", this.processMessage.bind(this));
        connection.on("close", this.close.bind(this));

        this.processQueue();
    }

    async initiateConversationWithDelay() {
        // Close existing transcription service if it exists
        if (this.transcriptionService) this.transcriptionService.dgConnection.requestClose();

        // Get Deepgram settings from the database
        try {
            const settings = await getDeepgramSettings();
            this.language = settings.language || this.language;
            this.utterance = settings.utterance || this.utterance;
            this.interruption = settings.interruption || this.interruption;

            logger.log(`Using Deepgram settings from database: language code=${this.language}, utterance=${this.utterance}, interruption=${this.interruption}`);
        } catch (error) {
            logger.error('Error getting Deepgram settings from database:', error);
            // Continue with default settings
        }

        // Initialize the transcription service with the settings
        this.transcriptionService = new TranscriptionService(this.language, this.interruption, this.utterance);
        this.transcriptionService.on('transcription', this.onTranscription.bind(this));
        this.transcriptionService.on('stopaispeaking', this.onStopAiSpeaking.bind(this));
    }


    onTranscription(transcription) {
        if (this.isConnected == false) return;
        this.onStopAiSpeaking();
        this.chatHistory.push({ role: 'user', content: transcription})
        promptLLM(this, this.aiTalking);
        this.agent_start_time = process.hrtime();
        logger.log(`${this.logDate()} ${this.aiTalking} User - ${transcription}`.green)
    }

    onStopAiSpeaking() {
        this.aiTalking++;
        ESLController.event({ command: `uuid_break ${this.uuid} all` })
    }

    async processMessage(message) {
        if (message.type === 'binary' && this.transcriptionService != null && this.isConnected ) {
            const rawAudio = message.binaryData;
            this.transcriptionService.send(rawAudio);
        } else if (message.type == "utf8"){
            let data = message.utf8Data;
            if (data == "actived") {
                // Create records directory inside /zoom_bot
                const recordsDir = path.join(zoomBotDir, 'records');
                if (!fs.existsSync(recordsDir)) {
                    fs.mkdirSync(recordsDir, { recursive: true });
                }

                // Use the call_id from the database for the recording filename
                const recordingFilename = `${this.call_id}.wav`;
                ESLController.event({ command: `uuid_record ${this.uuid} start ${path.join(recordsDir, recordingFilename)}` })

                // Don't add initial message or prompt the LLM
                // Just set the call as ready and wait for user input

                this.isReady = true;
                this.callDuration = process.hrtime();
            } else {
                this.uuid = data.split(",")[0];
                this.destination = data.split(",")[1];

                // Create call history record with progressing status
                this.createCallHistoryRecord().then(() => {
                    // Load settings from database
                    this.loadSettingsFromDatabase().then(() => {
                        // Start the conversation with a delay
                        this.initiateConversationWithDelay();
                    });
                });
            }
        }
    }

    async createCallHistoryRecord() {
        try {
            // Create initial call history entry with progressing status
            const callHistoryData = {
                phoneNumber: this.destination,
                callStartTime: new Date(),
                status: 'progressing'
            };

            // Save call history to database
            const callHistory = await CallHistory.create(callHistoryData);

            // Store the call history ID
            this.call_id = callHistory._id;

            logger.log(`Initial call history created with ID: ${this.call_id}`.green);

        } catch (error) {
            logger.error(`Error creating initial call history: ${error.message}`.red);
            // Generate a unique ID if we couldn't create a call history
            this.call_id = new Date().getTime();
        }
    }

    async close() {
        logger.info("FreeSWITCH: Connection closed".red);
        this.isConnected = false;
        global.callCount--;
        if (this.transcriptionService) this.transcriptionService.dgConnection.requestClose();

        // Generate transcript
        let transcribe = "";
        for (let i = 0; i < this.chatHistory.length; i++) {
            const chat = this.chatHistory[i];
            transcribe += chat.role + ": " + chat.content + "\n";
        }

        // Calculate call duration
        const endTime = process.hrtime(this.callDuration);
        const callDurationSeconds = Math.round(endTime[0] + endTime[1] / 1e9);
        const callDurationMs = (endTime[0] * 1000 + endTime[1] / 1e6).toFixed(4);
        logger.log(`Call ended. Duration: ${callDurationMs}ms (${callDurationSeconds}s)`);

        // Get call end time
        const callEndTime = new Date();

        try {
            // Extract caller information and generate call summary
            const { firstName, lastName, callSummary } = await this.extractCallerInfoAndSummary(transcribe);

            // Update call history with completed status and additional data
            const callHistoryData = {
                duration: callDurationSeconds,
                wasTransferred: false,
                callEndTime: callEndTime,
                transcript: transcribe,
                status: 'completed',
                transferredTo: '', // Initialize with empty string
                firstName: firstName,
                lastName: lastName,
                callSummary: callSummary,
                phoneNumber: this.destination
            };

            // Check if the call was transferred
            if (this.transferredTo) {
                callHistoryData.wasTransferred = true;
                callHistoryData.transferredTo = this.transferredTo;
            }

            let updatedCallHistory;

            // Update existing call history record
            if (this.call_id && mongoose.Types.ObjectId.isValid(this.call_id)) {
                updatedCallHistory = await CallHistory.findByIdAndUpdate(
                    this.call_id,
                    callHistoryData,
                    { new: true }
                );

                logger.log(`Call history updated with transcript and completed status. ID: ${this.call_id}`.green);
            } else {
                // If we don't have a valid call_id, create a new record
                callHistoryData.phoneNumber = this.destination;
                callHistoryData.callStartTime = new Date(callEndTime.getTime() - (callDurationSeconds * 1000));

                updatedCallHistory = await CallHistory.create(callHistoryData);
                this.call_id = updatedCallHistory._id;
                logger.log(`New call history created with transcript. ID: ${updatedCallHistory._id}`.green);
            }
        } catch (error) {
            logger.error(`Error updating call history: ${error.message}`.red);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    logDate() {
        const date = new Date(Date.now());

        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const seconds = String(date.getUTCSeconds()).padStart(2, '0');
        const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    async processQueue() {
        while (this.isConnected) {
            if (this.queue.length > 0) {
                try {
                    const { path, text, command, aiTalking, delay } = this.queue.shift();
                    if (command === 'hangup') {
                        logger.log(`hangup the call via command`.green);
                        this.delay(500)
                        ESLController.event({ command: `uuid_kill ${this.uuid}` });
                    } else if (aiTalking != this.aiTalking) {
                        continue;
                    } else {
                        const endTime1 = process.hrtime(this.agent_start_time);
                        const executionTimeInMs1 = (endTime1[0] * 1000 + endTime1[1] / 1e6).toFixed(4);
                        logger.log(`${this.logDate()} ${executionTimeInMs1 / 1000}s - ${aiTalking}:${this.aiTalking} - playing: ${text}`.gray)

                        this.chatHistory.push({ role: 'assistant', content: text});
                        ESLController.event({ command: `uuid_broadcast ${this.uuid} ${path} aleg` });

                        if (delay > 0) await this.delay(delay)
                    }
                } catch (e) {
                    logger.error(`ProcessQueue() has error ${e}`.red, e)
                }
            } else {
                await this.delay(5)
            }
        }
    }

    handleAudioData(audioStream, text, aiTalking) {
        return new Promise((resolve, reject) => {
            try {
                const wavStream = new PassThrough();
                audioStream.pipe(wavStream);
                let chunkBuffer = [];
                wavStream.on('data', async (chunk) => {
                    chunkBuffer.push(chunk);
                });

                wavStream.on('end', () => {
                    if (chunkBuffer.length > 0) {
                        const combinedChunk = Buffer.concat(chunkBuffer);

                        chunkBuffer = [];

                        const chunkPath = path.join(dirPath, `output_${this.uuid}_${++this.count}.wav`);
                        fs.writeFileSync(chunkPath, pcmToWav(combinedChunk));
                        this.queue.push({
                            path: chunkPath,
                            delay: this.calculateAudioDurationFromPcmLength(combinedChunk.length),
                            text: text,
                            command: "",
                            aiTalking: aiTalking,
                        })
                    }
                    resolve();
                });
            } catch (err) {
                reject(err);
            }
        });
    }

    calculateAudioDurationFromPcmLength(pcmDataLength, sampleRate=16000, channels=1, bitDepth=16) {
        const bytesPerSample = bitDepth / 8;  // 16 bits per sample = 2 bytes
        const totalSamples = pcmDataLength / (bytesPerSample * channels);

        const durationInSeconds = totalSamples / sampleRate;
        const durationInMilliseconds = durationInSeconds * 1000;

        return durationInMilliseconds;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    logDate() {
        const date = new Date(Date.now());

        const year = date.getUTCFullYear();
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const day = String(date.getUTCDate()).padStart(2, '0');
        const hours = String(date.getUTCHours()).padStart(2, '0');
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const seconds = String(date.getUTCSeconds()).padStart(2, '0');
        const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    async loadSettingsFromDatabase() {
        try {
            // Get active voice
            const activeVoice = await getActiveVoice();
            if (activeVoice) {
                this.voiceID = activeVoice.voiceId;

                // Get voice settings
                if (activeVoice.settings) {
                    this.stability = activeVoice.settings.stability || 0.75;
                    this.similarity = activeVoice.settings.similarity_boost || 0.75;
                }

                logger.log(`Using voice from database: ${activeVoice.name} (${this.voiceID})`);
            } else {
                // Use default voice ID if no active voice is found
                this.voiceID = "FGY2WhTYpPnrIDTdsKH5";
                logger.warn('No active voice found in the system, using default voice');
            }

            // Get ElevenLabs settings
            const elevenLabsSettings = await getElevenLabsSettings();
            this.stability = elevenLabsSettings.stability || this.stability;
            this.similarity = elevenLabsSettings.similarity || this.similarity;
            this.exaggeration = elevenLabsSettings.exaggeration || this.exaggeration;

            logger.log(`Using ElevenLabs settings: stability=${this.stability}, similarity=${this.similarity}, exaggeration=${this.exaggeration}`);

            // Get agent prompt
            const agentPromptData = await getAgentPrompt();

            // Build the prompt with system prompt, agent prompt, and department transfer mapping
            const londonTime = DateTime.now().setZone('Europe/London');
            this.prompt =
`${system_prompt}\n
## Language:\n Start the conversation in ${getLanguageFromCode(this.language) || "English"}.\n
## Call Type:\nThis call is ${this.callType} call. So must follow ${this.callType} call case.\n
## Current Time in London:\n${londonTime.toFormat('yyyy-MM-dd HH:mm:ss ZZZZ')}\n

${agentPromptData.prompt || ''}\n\n
`;

            // Welcome message functionality removed

            logger.log(`Agent prompt loaded from database (${agentPromptData.prompt ? agentPromptData.prompt.length : 0} chars)`);
        } catch (error) {
            logger.error('Error loading settings from database:', error);

            // Set default values if there's an error
            this.voiceID = "FGY2WhTYpPnrIDTdsKH5";

            // Build default prompt
            const londonTime = DateTime.now().setZone('Europe/London');

            this.prompt =
`${system_prompt}\n
## Language:\n Start the conversation in ${getLanguageFromCode(this.language) || "English"}.\n
## Call Type:\nThis call is ${this.callType} call. So must follow ${this.callType} call case.\n
## Current Time in London:\n${londonTime.toFormat('yyyy-MM-dd HH:mm:ss ZZZZ')}\n
`;
        }
    }

    // callTransfer function removed as it's no longer needed for the translator role

    /**
     * Extract caller information and generate a call summary using OpenAI
     * @param {string} transcript - The call transcript
     * @returns {Promise<Object>} - Object containing caller information and call summary
     */
    async extractCallerInfoAndSummary(transcript) {
        try {
            // System prompt for extracting caller information
            const systemPrompt = `
You are an AI assistant tasked with analyzing call transcripts.

Your task:
- Extract the caller's first name and last name from the transcript. If either name is not clearly mentioned, set that field to null.
- Write a concise summary of the call in 2-3 sentences. The callSummary field must always contain a relevant string summarizing the call, even if the details are limited.

Format your response as a plain JSON object, without any code block or extra text. For example:
{
  "firstName": "John",
  "lastName": "Smith",
  "callSummary": "The caller asked about the delivery status of their recent order. The agent provided estimated delivery times and offered to send tracking details."
}

If a name is not found, use "" for that field, for example:
{
  "firstName": "",
  "lastName": "",
  "callSummary": "The caller requested information about business hours. The agent explained the opening and closing times."
}

Do not include any explanations or formatting outside the JSON object. The output must always be valid JSON that can be parsed by JSON.parse().
`;

            // Get OpenAI model from database
            const openAISettings = await getOpenAISettings();
            const modelName = openAISettings.model || 'gpt-4-turbo'; // Default to gpt-4-turbo if not found

            logger.info(`Using OpenAI model from database for call summary: ${modelName}`);

            // Call OpenAI API to extract information
            const response = await openai.chat.completions.create({
                model: modelName,
                messages: [
                    { role: "system", content: systemPrompt },
                    { role: "user", content: transcript }
                ],
                temperature: 0.3,
                max_tokens: 500,
                response_format: { type: "json_object" }
            });

            // Parse the response
            const result = JSON.parse(response.choices[0].message.content);

            logger.log(`Extracted caller info: ${JSON.stringify(result)}`.green);

            return result;
        } catch (error) {
            logger.error(`Error extracting caller information: ${error.message}`.red, error);
            // Return default values if extraction fails
            return {
                firstName: null,
                lastName: null,
                callSummary: "Failed to generate call summary."
            };
        }
    }
}

module.exports = { MediaStream };

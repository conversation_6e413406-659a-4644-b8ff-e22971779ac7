import apiBase from './apiBase';
import { Prompt } from '../types';

const API_ENDPOINT = '/prompts';

// Get all prompts
export const getPrompts = async (): Promise<Prompt[]> => {
  try {
    const response = await apiBase.get(API_ENDPOINT);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch prompts');
  } catch (error: any) {
    console.error('Error fetching prompts:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch prompts');
  }
};

// Get prompts by type
export const getPromptsByType = async (type: string): Promise<Prompt[]> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/type/${type}`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || `Failed to fetch ${type} prompts`);
  } catch (error: any) {
    console.error(`Error fetching ${type} prompts:`, error);
    throw new Error(error.response?.data?.message || `Failed to fetch ${type} prompts`);
  }
};

// Get active prompts
export const getActivePrompts = async (): Promise<{system: Prompt, agent: Prompt, functional: Prompt[]}> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/active`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch active prompts');
  } catch (error: any) {
    console.error('Error fetching active prompts:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch active prompts');
  }
};

// Get a single prompt
export const getPrompt = async (id: string): Promise<Prompt> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/${id}`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch prompt');
  } catch (error: any) {
    console.error('Error fetching prompt:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch prompt');
  }
};

// Create a new prompt
export const createPrompt = async (promptData: {
  name: string;
  type: string;
  content: string;
  description?: string;
}): Promise<Prompt> => {
  try {
    const response = await apiBase.post(API_ENDPOINT, promptData);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to create prompt');
  } catch (error: any) {
    console.error('Error creating prompt:', error);
    throw new Error(error.response?.data?.message || 'Failed to create prompt');
  }
};

// Update a prompt
export const updatePrompt = async (id: string, promptData: {
  name?: string;
  content?: string;
  description?: string;
}): Promise<Prompt> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/${id}`, promptData);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to update prompt');
  } catch (error: any) {
    console.error('Error updating prompt:', error);
    throw new Error(error.response?.data?.message || 'Failed to update prompt');
  }
};

// Activate a prompt
export const activatePrompt = async (id: string): Promise<Prompt> => {
  try {
    const response = await apiBase.patch(`${API_ENDPOINT}/${id}/activate`, {});

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to activate prompt');
  } catch (error: any) {
    console.error('Error activating prompt:', error);
    throw new Error(error.response?.data?.message || 'Failed to activate prompt');
  }
};

// Delete a prompt
export const deletePrompt = async (id: string): Promise<void> => {
  try {
    const response = await apiBase.delete(`${API_ENDPOINT}/${id}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete prompt');
    }
  } catch (error: any) {
    console.error('Error deleting prompt:', error);
    throw new Error(error.response?.data?.message || 'Failed to delete prompt');
  }
};

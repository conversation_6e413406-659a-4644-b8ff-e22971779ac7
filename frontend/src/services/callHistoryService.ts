import apiBase from './apiBase';
import { CallHistory, CallStats } from '../types';

const API_ENDPOINT = '/call-history';

// Get all call history with pagination and filters
export const getCallHistory = async (
  page: number = 1, 
  limit: number = 10,
  filters: {
    startDate?: string;
    endDate?: string;
    phoneNumber?: string;
    wasTransferred?: boolean;
  } = {}
): Promise<{
  data: CallHistory[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
  };
  stats: CallStats;
  total: number;
}> => {
  try {
    // Build query params
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    
    // Add filters if provided
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.phoneNumber) params.append('phoneNumber', filters.phoneNumber);
    if (filters.wasTransferred !== undefined) params.append('wasTransferred', filters.wasTransferred.toString());
    
    const response = await apiBase.get(`${API_ENDPOINT}?${params.toString()}`);
    
    if (response.data.success) {
      return {
        data: response.data.data,
        pagination: response.data.pagination,
        stats: response.data.stats,
        total: response.data.total
      };
    }
    
    throw new Error(response.data.message || 'Failed to fetch call history');
  } catch (error: any) {
    console.error('Error fetching call history:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch call history');
  }
};

// Get call stats
export const getCallStats = async (): Promise<CallStats> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/stats`);
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch call stats');
  } catch (error: any) {
    console.error('Error fetching call stats:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch call stats');
  }
};

// Get a single call history entry
export const getCallHistoryEntry = async (id: string): Promise<CallHistory> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/${id}`);
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to fetch call history entry');
  } catch (error: any) {
    console.error('Error fetching call history entry:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch call history entry');
  }
};

// Create a new call history entry
export const createCallHistoryEntry = async (callData: Partial<CallHistory>): Promise<CallHistory> => {
  try {
    const response = await apiBase.post(API_ENDPOINT, callData);
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to create call history entry');
  } catch (error: any) {
    console.error('Error creating call history entry:', error);
    throw new Error(error.response?.data?.message || 'Failed to create call history entry');
  }
};

// Update a call history entry
export const updateCallHistoryEntry = async (id: string, callData: Partial<CallHistory>): Promise<CallHistory> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/${id}`, callData);
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to update call history entry');
  } catch (error: any) {
    console.error('Error updating call history entry:', error);
    throw new Error(error.response?.data?.message || 'Failed to update call history entry');
  }
};

// Delete a call history entry
export const deleteCallHistoryEntry = async (id: string): Promise<void> => {
  try {
    const response = await apiBase.delete(`${API_ENDPOINT}/${id}`);
    
    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete call history entry');
    }
  } catch (error: any) {
    console.error('Error deleting call history entry:', error);
    throw new Error(error.response?.data?.message || 'Failed to delete call history entry');
  }
};

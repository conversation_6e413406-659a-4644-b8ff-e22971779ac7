import apiBase from './apiBase';
import { ZoomMeetingResponse, ZoomMeetingLog } from '../types';

const API_ENDPOINT = '/zoom';

// Create a new Zoom meeting
export const createZoomMeeting = async (
  meetingDetails: {
    topic: string;
    agenda?: string;
    start_time?: string;
    duration?: number;
    timezone?: string;
    join_with_agent?: boolean;
    caller_id?: string;
  }
): Promise<ZoomMeetingResponse> => {
  try {
    const response = await apiBase.post(API_ENDPOINT, meetingDetails);
    return response.data;
  } catch (error: any) {
    console.error('Error creating Zoom meeting:', error);
    throw new Error(error.response?.data?.message || 'Failed to create Zoom meeting');
  }
};

// Get SIP information for an existing Zoom meeting
export const getZoomMeetingSIPInfo = async (meetingId: string): Promise<ZoomMeetingResponse> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/${meetingId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting Zoom meeting SIP info:', error);
    throw new Error(error.response?.data?.message || 'Failed to get Zoom meeting SIP info');
  }
};

// Get Zoom meeting logs
export const getZoomMeetingLogs = async (): Promise<ZoomMeetingLog[]> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/logs`);
    return response.data.data || [];
  } catch (error: any) {
    console.error('Error getting Zoom meeting logs:', error);
    throw new Error(error.response?.data?.message || 'Failed to get Zoom meeting logs');
  }
};

// Join an existing Zoom meeting with the AI agent
export const joinZoomMeeting = async (
  meetingDetails: {
    meeting_id: string;
    passcode?: string;
    caller_id?: string;
  }
): Promise<ZoomMeetingResponse> => {
  try {
    const response = await apiBase.post(`${API_ENDPOINT}/join`, meetingDetails);

    // Check if the agent call was successful
    if (response.data.agent_call && response.data.agent_call.status !== 'SUCCESS') {
      const errorMessage = response.data.agent_call.error ||
        'Failed to connect to the meeting. This may be due to SIP configuration issues.';
      console.warn('Agent call failed but API call succeeded:', errorMessage);

      // Don't throw an error here, just return the data with the error status
      // This allows the UI to show the meeting details but with an error status for the agent
    }

    return response.data;
  } catch (error: any) {
    console.error('Error joining Zoom meeting:', error);
    throw new Error(
      error.response?.data?.error ||
      'Failed to join Zoom meeting. Please check that the meeting ID and passcode are correct.'
    );
  }
};

import axios from 'axios';
import { User } from '../types';

const API_URL = import.meta.env.VITE_API_URL + '/api/auth';

// Sign up a new user
export const signup = async (userData: { name: string; email: string; password: string }): Promise<{ token: string; user: User }> => {
  try {
    const response = await axios.post(`${API_URL}/signup`, userData);
    
    if (response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data.message || 'Failed to sign up');
  } catch (error: any) {
    console.error('Error signing up:', error);
    throw new Error(error.response?.data?.message || 'Failed to sign up. Please try again.');
  }
};

// Sign in a user
export const signin = async (credentials: { email: string; password: string }): Promise<{ token: string; user: User }> => {
  try {
    const response = await axios.post(`${API_URL}/signin`, credentials);
    
    if (response.data.success) {
      return response.data;
    }
    
    throw new Error(response.data.message || 'Failed to sign in');
  } catch (error: any) {
    console.error('Error signing in:', error);
    throw new Error(error.response?.data?.message || 'Failed to sign in. Please check your credentials.');
  }
};

// Sign out a user
export const signout = async (): Promise<void> => {
  try {
    await axios.get(`${API_URL}/signout`);
  } catch (error) {
    console.error('Error signing out:', error);
  }
};

// Get current user
export const getCurrentUser = async (): Promise<User> => {
  try {
    const token = localStorage.getItem('token');
    
    if (!token) {
      throw new Error('No authentication token found');
    }
    
    const response = await axios.get(`${API_URL}/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    if (response.data.success) {
      return response.data.data;
    }
    
    throw new Error(response.data.message || 'Failed to get user data');
  } catch (error: any) {
    console.error('Error getting current user:', error);
    throw new Error(error.response?.data?.message || 'Failed to get user data');
  }
};

import apiBase from './apiBase';
import { Document } from '../types';

const API_ENDPOINT = '/documents';

// Get all documents
export const getDocuments = async (): Promise<Document[]> => {
  try {
    const response = await apiBase.get(API_ENDPOINT);
    return response.data.data || [];
  } catch (error) {
    console.error('Error in getDocuments:', error);
    throw error;
  }
};

// Get a single document
export const getDocument = async (id: string): Promise<Document> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/${id}`);
    return response.data.data;
  } catch (error) {
    console.error('Error in getDocument:', error);
    throw error;
  }
};

// Upload a document
export const uploadDocument = async (formData: FormData): Promise<Document> => {
  try {
    const response = await apiBase.post(API_ENDPOINT, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error in uploadDocument:', error);
    throw error;
  }
};

// Update a document
export const updateDocument = async (id: string, documentData: Partial<Document>): Promise<Document> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/${id}`, documentData);
    return response.data.data;
  } catch (error) {
    console.error('Error in updateDocument:', error);
    throw error;
  }
};

// Delete a document
export const deleteDocument = async (id: string): Promise<void> => {
  try {
    await apiBase.delete(`${API_ENDPOINT}/${id}`);
  } catch (error) {
    console.error('Error in deleteDocument:', error);
    throw error;
  }
};

// Get document download URL
export const getDocumentDownloadUrl = (id: string): string => {
  return `${apiBase.defaults.baseURL}${API_ENDPOINT}/${id}/download`;
};

import apiBase from './apiBase';
import { User } from '../types';

const API_ENDPOINT = '/users';

// Get user profile
export const getUserProfile = async (): Promise<User> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/profile`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch user profile');
  } catch (error: any) {
    console.error('Error fetching user profile:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch user profile');
  }
};

// Update user profile
export const updateUserProfile = async (userData: { name?: string; email?: string }): Promise<User> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/profile`, userData);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to update user profile');
  } catch (error: any) {
    console.error('Error updating user profile:', error);
    throw new Error(error.response?.data?.message || 'Failed to update user profile');
  }
};

// Change user password
export const changeUserPassword = async (passwordData: { currentPassword: string; newPassword: string }): Promise<void> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/change-password`, passwordData);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to change password');
    }
  } catch (error: any) {
    console.error('Error changing password:', error);
    throw new Error(error.response?.data?.message || 'Failed to change password');
  }
};

import apiBase from './apiBase';

export interface ElevenLabsVoice {
  voice_id: string;
  name: string;
  category?: string;
  description?: string;
  preview_url?: string;
  labels?: {
    accent?: string;
    age?: string;
    gender?: string;
    use_case?: string;
    description?: string;
  };
  settings?: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
}

export interface ElevenLabsVoicesResponse {
  voices: ElevenLabsVoice[];
  has_more: boolean;
  total_count: number;
}

// Get all voices from ElevenLabs API via backend
export const getElevenLabsVoices = async (): Promise<ElevenLabsVoice[]> => {
  try {
    // Call our backend endpoint that securely accesses ElevenLabs
    const response = await apiBase.get('/elevenlabs/voices');

    if (response.data.success) {
      return response.data.data.voices || [];
    }

    throw new Error(response.data.message || 'Failed to fetch voices from ElevenLabs');
  } catch (error: any) {
    console.error('Error fetching ElevenLabs voices:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch voices from ElevenLabs');
  }
};

// Get ElevenLabs settings
export const getElevenLabsSettings = async (): Promise<{
  stability: number;
  similarity: number;
  exaggeration: number;
}> => {
  try {
    const response = await apiBase.get('/elevenlabs/settings');

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch ElevenLabs settings');
  } catch (error: any) {
    console.error('Error fetching ElevenLabs settings:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch ElevenLabs settings');
  }
};

// Play a voice preview
export const playVoicePreview = (previewUrl: string): Promise<HTMLAudioElement> => {
  return new Promise((resolve, reject) => {
    const audio = new Audio(previewUrl);

    audio.onended = () => {
      resolve(audio);
    };

    audio.onerror = (error) => {
      console.error('Error playing audio:', error);
      reject(error);
    };

    audio.play()
      .then(() => {
        // Audio started playing successfully
      })
      .catch(error => {
        console.error('Error playing audio:', error);
        reject(error);
      });

    return audio;
  });
};

// Verify an ElevenLabs voice ID
export const verifyElevenLabsVoiceId = async (voiceId: string): Promise<ElevenLabsVoice> => {
  try {
    const response = await apiBase.get(`/elevenlabs/voices/${voiceId}/verify`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to verify voice ID');
  } catch (error: any) {
    console.error('Error verifying ElevenLabs voice ID:', error);
    throw new Error(error.response?.data?.message || 'Failed to verify voice ID');
  }
};

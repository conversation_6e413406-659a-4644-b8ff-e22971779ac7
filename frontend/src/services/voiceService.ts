import apiBase from './apiBase';
import { Voice } from '../types';
import { ElevenLabsVoice } from './elevenLabsService';

const API_ENDPOINT = '/voices';

// Get all voices
export const getVoices = async (): Promise<Voice[]> => {
  try {
    const response = await apiBase.get(API_ENDPOINT);
    return response.data.data || [];
  } catch (error) {
    console.error('Error in getVoices:', error);
    return [];
  }
};

// Get active voice
export const getActiveVoice = async (): Promise<Voice | null> => {
  try {
    const voices = await getVoices();
    return voices.find(voice => voice.isActive) || null;
  } catch (error) {
    console.error('Error in getActiveVoice:', error);
    return null;
  }
};

// Get a single voice
export const getVoice = async (id: string): Promise<Voice> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/${id}`);
    return response.data.data;
  } catch (error) {
    console.error('Error in getVoice:', error);
    throw error;
  }
};

// Create a new voice
export const createVoice = async (voiceData: Partial<Voice>): Promise<Voice> => {
  try {
    const response = await apiBase.post(API_ENDPOINT, voiceData);
    return response.data.data;
  } catch (error) {
    console.error('Error in createVoice:', error);
    throw error;
  }
};

// Update a voice
export const updateVoice = async (id: string, voiceData: Partial<Voice>): Promise<Voice> => {
  try {
    const response = await apiBase.put(`${API_ENDPOINT}/${id}`, voiceData);
    return response.data.data;
  } catch (error) {
    console.error('Error in updateVoice:', error);
    throw error;
  }
};

// Activate a voice
export const activateVoice = async (id: string): Promise<Voice> => {
  try {
    const response = await apiBase.patch(`${API_ENDPOINT}/${id}/activate`);
    return response.data.data;
  } catch (error) {
    console.error('Error in activateVoice:', error);
    throw error;
  }
};

// Activate an ElevenLabs voice directly
export const activateElevenLabsVoice = async (voice: ElevenLabsVoice): Promise<Voice> => {
  try {
    const response = await apiBase.post(`${API_ENDPOINT}/elevenlabs/activate`, {
      voiceId: voice.voice_id,
      name: voice.name,
      description: voice.description || `${voice.name} voice from ElevenLabs`,
      previewUrl: voice.preview_url,
      settings: voice.settings || {
        stability: 0.75,
        similarity_boost: 0.75
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error in activateElevenLabsVoice:', error);
    throw error;
  }
};

// Activate a custom ElevenLabs voice ID
export const activateCustomVoiceId = async (
  voiceId: string,
  name: string,
  description?: string,
  previewUrl?: string,
  settings?: { stability: number; similarity_boost: number }
): Promise<Voice> => {
  try {
    const response = await apiBase.post(`${API_ENDPOINT}/elevenlabs/activate`, {
      voiceId,
      name,
      description: description || `${name} voice from ElevenLabs`,
      previewUrl,
      settings: settings || {
        stability: 0.75,
        similarity_boost: 0.75
      }
    });
    return response.data.data;
  } catch (error) {
    console.error('Error in activateCustomVoiceId:', error);
    throw error;
  }
};

// Delete a voice
export const deleteVoice = async (id: string): Promise<void> => {
  try {
    await apiBase.delete(`${API_ENDPOINT}/${id}`);
  } catch (error) {
    console.error('Error in deleteVoice:', error);
    throw error;
  }
};

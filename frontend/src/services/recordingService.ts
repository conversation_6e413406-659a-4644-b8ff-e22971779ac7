import apiBase from './apiBase';

export interface Recording {
  id: string;
  phoneNumber: string;
  duration: number;
  formattedDuration: string;
  callStartTime: string;
  callEndTime: string;
  fileSize: number;
  downloadUrl: string;
  streamUrl: string;
}

export interface RecordingDetails extends Recording {
  transcript?: string;
}

export interface RecordingsResponse {
  success: boolean;
  count: number;
  total: number;
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
  };
  data: Recording[];
}

// Get all recordings with pagination
export const getRecordings = async (
  page = 1,
  limit = 10,
  filters: { startDate?: string; endDate?: string; phoneNumber?: string } = {}
): Promise<RecordingsResponse> => {
  try {
    let url = `/recordings?page=${page}&limit=${limit}`;

    // Add filters if provided
    if (filters.startDate && filters.endDate) {
      url += `&startDate=${filters.startDate}&endDate=${filters.endDate}`;
    }

    if (filters.phoneNumber) {
      url += `&phoneNumber=${filters.phoneNumber}`;
    }

    const response = await apiBase.get(url);

    if (response.data.success) {
      return response.data;
    }

    throw new Error(response.data.message || 'Failed to fetch recordings');
  } catch (error: any) {
    console.error('Error fetching recordings:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch recordings');
  }
};

// Get recording details
export const getRecording = async (id: string): Promise<RecordingDetails> => {
  try {
    const response = await apiBase.get(`/recordings/${id}`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch recording details');
  } catch (error: any) {
    console.error('Error fetching recording details:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch recording details');
  }
};

// Helper function to download a recording
export const downloadRecording = (id: string): void => {
  // Get the API URL from environment
  const API_URL = import.meta.env.VITE_API_URL;

  // Create a hidden anchor element
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = `${API_URL}/api/recordings/${id}/download`;

  // Add auth token to download URL
  const token = localStorage.getItem('token');
  if (token) {
    a.href += `?token=${token}`;
  }

  console.log('Downloading from URL:', a.href);

  // Trigger download
  document.body.appendChild(a);
  a.click();

  // Clean up
  window.URL.revokeObjectURL(a.href);
  document.body.removeChild(a);
};

import apiBase from './apiBase';

export interface ApiKeys {
  openaiApiKey: string;
  deepgramApiKey: string;
  deepgramLanguage: string;
  deepgramUtterance: number;
  deepgramInterruption: number;
  elevenlabsApiKey: string;
  elevenlabsStability: number;
  elevenlabsSimilarity: number;
  elevenlabsExaggeration: number;
  elevenlabsModel: string;
  openaiModel: string;
  zoomClientId: string;
  zoomClientSecret: string;
  zoomAccountId: string;
  zoomContactEmail: string;
  zoomContactName: string;
}

export interface DeepgramLanguage {
  code: string;
  name: string;
}

export interface OpenAIModel {
  id: string;
  name: string;
}

export interface ElevenLabsModel {
  model_id: string;
  name: string;
  description: string;
  can_use_style: boolean;
  can_use_speaker_boost: boolean;
  token_cost_factor: number;
  languages: { language_id: string; name: string }[];
}

const API_ENDPOINT = '/settings/api-keys';

// Get API keys
export const getApiKeys = async (): Promise<ApiKeys> => {
  try {
    const response = await apiBase.get(API_ENDPOINT);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch API keys');
  } catch (error: any) {
    console.error('Error fetching API keys:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch API keys');
  }
};

// Update API keys
export const updateApiKeys = async (apiKeys: Partial<ApiKeys>): Promise<ApiKeys> => {
  try {
    const response = await apiBase.put(API_ENDPOINT, apiKeys);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to update API keys');
  } catch (error: any) {
    console.error('Error updating API keys:', error);
    throw new Error(error.response?.data?.message || 'Failed to update API keys');
  }
};

// Validate OpenAI API key
export const validateOpenAIKey = async (apiKey: string): Promise<{ success: boolean; data?: any; message?: string }> => {
  try {
    const response = await apiBase.post(`${API_ENDPOINT}/validate/openai`, { apiKey });

    return {
      success: response.data.success,
      data: response.data.data,
      message: response.data.message
    };
  } catch (error: any) {
    console.error('Error validating OpenAI API key:', error);
    return {
      success: false,
      message: error.response?.data?.message || 'Failed to validate OpenAI API key'
    };
  }
};

// Get available OpenAI models
export const getOpenAIModels = async (): Promise<OpenAIModel[]> => {
  try {
    const response = await apiBase.get(`${API_ENDPOINT}/openai-models`);

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch OpenAI models');
  } catch (error: any) {
    console.error('Error fetching OpenAI models:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch OpenAI models');
  }
};

// Get available ElevenLabs models
export const getElevenLabsModels = async (): Promise<ElevenLabsModel[]> => {
  try {
    const response = await apiBase.get('/elevenlabs/models');

    if (response.data.success) {
      return response.data.data;
    }

    throw new Error(response.data.message || 'Failed to fetch ElevenLabs models');
  } catch (error: any) {
    console.error('Error fetching ElevenLabs models:', error);
    throw new Error(error.response?.data?.message || 'Failed to fetch ElevenLabs models');
  }
};

// Get available Deepgram languages
export const getDeepgramLanguages = (): DeepgramLanguage[] => {
  return [
    { code: 'bg', name: 'Bulgarian' },
    { code: 'ca', name: 'Catalan' },
    { code: 'zh', name: 'Chinese (Mandarin, Simplified)' },
    { code: 'zh-CN', name: 'Chinese (Mandarin, Simplified)' },
    { code: 'zh-Hans', name: 'Chinese (Mandarin, Simplified)' },
    { code: 'zh-TW', name: 'Chinese (Mandarin, Traditional)' },
    { code: 'zh-Hant', name: 'Chinese (Mandarin, Traditional)' },
    { code: 'zh-HK', name: 'Chinese (Cantonese, Traditional)' },
    { code: 'cs', name: 'Czech' },
    { code: 'da', name: 'Danish' },
    { code: 'da-DK', name: 'Danish' },
    { code: 'nl', name: 'Dutch' },
    { code: 'en', name: 'English' },
    { code: 'en-US', name: 'English (US)' },
    { code: 'en-AU', name: 'English (Australia)' },
    { code: 'en-GB', name: 'English (UK)' },
    { code: 'en-NZ', name: 'English (New Zealand)' },
    { code: 'en-IN', name: 'English (India)' },
    { code: 'et', name: 'Estonian' },
    { code: 'fi', name: 'Finnish' },
    { code: 'nl-BE', name: 'Flemish' },
    { code: 'fr', name: 'French' },
    { code: 'fr-CA', name: 'French (Canada)' },
    { code: 'de', name: 'German' },
    { code: 'de-CH', name: 'German (Switzerland)' },
    { code: 'el', name: 'Greek' },
    { code: 'hi', name: 'Hindi' },
    { code: 'hu', name: 'Hungarian' },
    { code: 'id', name: 'Indonesian' },
    { code: 'it', name: 'Italian' },
    { code: 'ja', name: 'Japanese' },
    { code: 'ko', name: 'Korean' },
    { code: 'ko-KR', name: 'Korean' },
    { code: 'lv', name: 'Latvian' },
    { code: 'lt', name: 'Lithuanian' },
    { code: 'ms', name: 'Malay' },
    { code: 'no', name: 'Norwegian' },
    { code: 'pl', name: 'Polish' },
    { code: 'pt', name: 'Portuguese' },
    { code: 'pt-BR', name: 'Portuguese (Brazil)' },
    { code: 'pt-PT', name: 'Portuguese (Portugal)' },
    { code: 'ro', name: 'Romanian' },
    { code: 'ru', name: 'Russian' },
    { code: 'sk', name: 'Slovak' },
    { code: 'es', name: 'Spanish' },
    { code: 'es-419', name: 'Spanish (Latin America)' },
    { code: 'sv', name: 'Swedish' },
    { code: 'sv-SE', name: 'Swedish' },
    { code: 'th', name: 'Thai' },
    { code: 'th-TH', name: 'Thai' },
    { code: 'tr', name: 'Turkish' },
    { code: 'uk', name: 'Ukrainian' },
    { code: 'vi', name: 'Vietnamese' }
  ];
};

export interface User {
  id: string;
  email: string;
  name?: string;
}

export interface Voice {
  _id: string;
  name: string;
  voiceId: string;
  description: string;
  previewUrl?: string;
  isActive: boolean;
  settings?: {
    stability: number;
    similarity_boost: number;
  };
  user: string;
  createdAt: string;
  updatedAt: string;
}

export interface Prompt {
  _id: string;
  name: string;
  type: 'system' | 'agent' | 'functional';
  content: string;
  description?: string;
  isActive: boolean;
  user: string;
  createdAt: string;
  updatedAt: string;
}



export interface CallActivity {
  id: string;
  timestamp: string;
  phoneNumber: string;
  duration: string;
  wasTransferred: boolean;
  transferredTo?: string;
}

export interface CallHistory {
  _id: string;
  phoneNumber: string;
  duration: number;
  formattedDuration: string;
  wasTransferred: boolean;
  transferredTo?: string;

  user: string;
  callStartTime: string;
  callEndTime?: string;
  transcript?: string;
  createdAt: string;
  // Recording related fields
  recordingUrl?: string;
  downloadUrl?: string;
  fileSize?: number;
}

export interface CallStats {
  totalCalls: number;
  transferredCalls: number;
  totalDuration: number;
  recentCalls: number;
  changePercentage: string;
}



export interface Document {
  _id: string;
  name: string;
  originalFilename: string;
  fileType: string;
  fileSize: number;
  filePath: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ZoomMeeting {
  id: string;
  topic: string;
  start_time: string;
  duration: number;
  join_url: string;
  password: string;
}

export interface ZoomSipInfo {
  uri: string | null;
  enabled: boolean;
  invitation: string | null;
}

export interface ZoomAgentCall {
  status: string;
  uuid: string | null;
  timestamp: string;
  error?: string;
}

export interface ZoomMeetingResponse {
  meeting: ZoomMeeting;
  sip: ZoomSipInfo;
  agent_call?: ZoomAgentCall;
}

export interface ZoomMeetingLog {
  _id: string;
  meeting: ZoomMeeting;
  sip: ZoomSipInfo;
  agent_call?: ZoomAgentCall;
  createdAt: string;
  user: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}
import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '../components/ui/Card';
import { PhoneCall, Clock, Search, FileAudio } from 'lucide-react';
import { CallHistory, CallStats } from '../types';
import { getCallHistory } from '../services/callHistoryService';
import CallHistoryDetailModal from '../components/CallHistoryDetailModal';
import Input from '../components/ui/Input';
import toast from 'react-hot-toast';

const Dashboard: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [callHistory, setCallHistory] = useState<CallHistory[]>([]);
  const [callStats, setCallStats] = useState<CallStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCallId, setSelectedCallId] = useState<string | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Fetch call history data
  useEffect(() => {
    const fetchCallHistory = async () => {
      try {
        setIsLoading(true);
        const result = await getCallHistory(currentPage, 10, {
          phoneNumber: searchQuery || undefined
        });

        setCallHistory(result.data);
        setCallStats(result.stats);
        setTotalPages(result.pagination.totalPages);
      } catch (error) {
        toast.error('Failed to load call history');
        console.error('Error fetching call history:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCallHistory();
  }, [currentPage, searchQuery]);

  // Handle search input change with debounce
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Format duration for display
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes === 0) {
      return `${remainingSeconds}s`;
    }

    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-500 dark:text-slate-400">
                  Total Calls
                </p>
                <div className="flex items-baseline mt-1">
                  <p className="text-2xl font-semibold text-slate-900 dark:text-white">
                    {callStats?.totalCalls || 0}
                  </p>
                  <p className={`ml-2 text-sm font-medium ${callStats?.changePercentage.startsWith('+') ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {callStats?.changePercentage || '0%'}
                  </p>
                </div>
              </div>
              <PhoneCall className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-500 dark:text-slate-400">
                  Total Duration
                </p>
                <div className="flex items-baseline mt-1">
                  <p className="text-2xl font-semibold text-slate-900 dark:text-white">
                    {callStats ? formatDuration(callStats.totalDuration) : '0m 0s'}
                  </p>
                  <p className="ml-2 text-sm font-medium text-slate-600 dark:text-slate-400">
                    {callStats?.totalCalls ? (
                      `Avg: ${formatDuration(Math.round(callStats.totalDuration / callStats.totalCalls))}`
                    ) : 'Avg: 0s'}
                  </p>
                </div>
              </div>
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Recent Call Activity</CardTitle>
            <div className="w-64">
              <Input
                placeholder="Search phone number..."
                value={searchQuery}
                onChange={handleSearchChange}
                icon={<Search size={18} />}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              {callHistory.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="text-left border-b border-slate-200 dark:border-slate-700">
                        <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Time</th>
                        <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Phone Number</th>
                        <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Duration</th>
                        <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Status</th>
                        <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium text-center">Recording</th>
                      </tr>
                    </thead>
                    <tbody>
                      {callHistory.map((call) => (
                        <tr
                          key={call._id}
                          className="border-b border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50 cursor-pointer"
                          onClick={() => { setSelectedCallId(call._id); setShowDetailModal(true); }}
                        >
                          <td className="px-4 py-3 text-slate-900 dark:text-white">
                            {formatDate(call.callStartTime)}
                          </td>
                          <td className="px-4 py-3 text-slate-900 dark:text-white">
                            {call.phoneNumber}
                          </td>
                          <td className="px-4 py-3 text-slate-900 dark:text-white">
                            {call.formattedDuration}
                          </td>
                          <td className="px-4 py-3">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200">
                              AI Translator
                            </span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            {call.callEndTime && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <FileAudio size={14} className="mr-1" />
                                Available
                              </span>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                  No call activity found
                  {searchQuery && ' matching your search criteria'}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-6 space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={`px-3 py-1 rounded ${currentPage === 1 ? 'bg-slate-100 text-slate-400 cursor-not-allowed dark:bg-slate-700 dark:text-slate-500' : 'bg-slate-200 text-slate-700 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'}`}
                  >
                    Previous
                  </button>
                  <div className="px-3 py-1 bg-slate-100 dark:bg-slate-800 rounded">
                    Page {currentPage} of {totalPages}
                  </div>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={`px-3 py-1 rounded ${currentPage === totalPages ? 'bg-slate-100 text-slate-400 cursor-not-allowed dark:bg-slate-700 dark:text-slate-500' : 'bg-slate-200 text-slate-700 hover:bg-slate-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'}`}
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Call Detail Modal */}
      {showDetailModal && selectedCallId && (
        <CallHistoryDetailModal
          callId={selectedCallId}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedCallId(null);
          }}
        />
      )}
    </div>
  );
};

export default Dashboard;
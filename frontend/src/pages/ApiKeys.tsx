import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Select from '../components/ui/Select';
import { Key, Check, AlertCircle, RefreshCw } from 'lucide-react';
import {
  ApiKeys,
  OpenAIModel,
  DeepgramLanguage,
  ElevenLabsModel,
  getApiKeys,
  updateApiKeys,
  validateOpenAIKey,
  getOpenAIModels,
  getElevenLabsModels,
  getDeepgramLanguages
} from '../services/apiKeyService';
import toast from 'react-hot-toast';

const ApiKeysPage: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKeys>({
    openaiApiKey: '',
    deepgramApiKey: '',
    deepgramLanguage: 'en',
    deepgramUtterance: 1100,
    deepgramInterruption: 150,
    elevenlabsApiKey: '',
    elevenlabsStability: 0.5,
    elevenlabsSimilarity: 0.8,
    elevenlabsExaggeration: 0.0,
    elevenlabsModel: 'eleven_multilingual_v2',
    openaiModel: 'gpt-3.5-turbo',
    zoomClientId: '',
    zoomClientSecret: '',
    zoomAccountId: '',
    zoomContactEmail: '',
    zoomContactName: ''
  });

  const [deepgramLanguages, setDeepgramLanguages] = useState<DeepgramLanguage[]>([]);

  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [isLoadingElevenLabsModels, setIsLoadingElevenLabsModels] = useState(false);
  const [openaiModels, setOpenaiModels] = useState<OpenAIModel[]>([]);
  const [elevenLabsModels, setElevenLabsModels] = useState<ElevenLabsModel[]>([]);
  const [validationStatus, setValidationStatus] = useState<'none' | 'success' | 'error'>('none');

  // Fetch API keys on component mount
  useEffect(() => {
    fetchApiKeys();
    // Load Deepgram languages
    setDeepgramLanguages(getDeepgramLanguages());
  }, []);

  // Fetch OpenAI models when API keys are loaded and OpenAI key exists
  useEffect(() => {
    // Only attempt to fetch models if:
    // 1. We're not currently loading the API keys
    // 2. We have an OpenAI API key
    // 3. We're not already loading models
    if (apiKeys.openaiApiKey && apiKeys.openaiApiKey.length > 0) {
      // Don't show toast when loading models on page initialization
      fetchOpenAIModels(false);
    }
  }, [apiKeys.openaiApiKey]);

  // Fetch ElevenLabs models when API keys are loaded and ElevenLabs key exists
  useEffect(() => {
    if (apiKeys.elevenlabsApiKey && apiKeys.elevenlabsApiKey.length > 0) {
      // Don't show toast when loading models on page initialization
      fetchElevenLabsModels(false);
    }
  }, [apiKeys.elevenlabsApiKey]);

  const fetchApiKeys = async () => {
    try {
      setIsLoading(true);
      const data = await getApiKeys();
      setApiKeys(data);
    } catch (error) {
      toast.error('Failed to load API keys');
      console.error('Error fetching API keys:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchOpenAIModels = async (showToast = false) => {
    try {
      setIsLoadingModels(true);
      const models = await getOpenAIModels();
      setOpenaiModels(models);

      // Only show success toast if explicitly requested (e.g., when button is clicked)
      if (models.length > 0 && showToast) {
        toast.success('Successfully loaded OpenAI models');
      }
    } catch (error) {
      // Only show error toast if explicitly requested or if there's an error
      if (showToast) {
        toast.error('Failed to load OpenAI models. Please check your API key.');
      }
      console.error('Error fetching OpenAI models:', error);
    } finally {
      setIsLoadingModels(false);
    }
  };

  const fetchElevenLabsModels = async (showToast = false) => {
    try {
      setIsLoadingElevenLabsModels(true);
      const models = await getElevenLabsModels();
      setElevenLabsModels(models);

      // Only show success toast if explicitly requested (e.g., when button is clicked)
      if (models.length > 0 && showToast) {
        toast.success('Successfully loaded ElevenLabs models');
      }
    } catch (error) {
      // Only show error toast if explicitly requested or if there's an error
      if (showToast) {
        toast.error('Failed to load ElevenLabs models. Please check your API key.');
      }
      console.error('Error fetching ElevenLabs models:', error);
    } finally {
      setIsLoadingElevenLabsModels(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Handle number inputs
    if (name === 'deepgramUtterance') {
      const utterance = parseInt(value);
      if (!isNaN(utterance)) {
        // Ensure the value is within the allowed range
        const validValue = Math.min(Math.max(utterance, 0), 5000);
        setApiKeys(prev => ({
          ...prev,
          [name]: validValue
        }));
      }
    } else if (name === 'deepgramInterruption') {
      const interruption = parseInt(value);
      if (!isNaN(interruption)) {
        // Ensure the value is within the allowed range
        const validValue = Math.min(Math.max(interruption, 0), 1000);
        setApiKeys(prev => ({
          ...prev,
          [name]: validValue
        }));
      }
    } else if (name === 'elevenlabsStability' || name === 'elevenlabsSimilarity' || name === 'elevenlabsExaggeration') {
      const floatValue = parseFloat(value);
      if (!isNaN(floatValue)) {
        // Ensure the value is within the allowed range (0-1)
        const validValue = Math.min(Math.max(floatValue, 0), 1);
        setApiKeys(prev => ({
          ...prev,
          [name]: validValue
        }));
      }
    } else {
      // Handle string inputs
      setApiKeys(prev => ({
        ...prev,
        [name]: value
      }));
    }

    // Reset validation status when OpenAI key changes
    if (name === 'openaiApiKey') {
      setValidationStatus('none');
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      const updatedKeys = await updateApiKeys(apiKeys);
      setApiKeys(updatedKeys);
      toast.success('API keys saved successfully');
    } catch (error) {
      toast.error('Failed to save API keys');
      console.error('Error saving API keys:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleValidateOpenAI = async () => {
    try {
      setIsValidating(true);
      setValidationStatus('none');

      // Only validate if the key is not masked
      if (apiKeys.openaiApiKey && !apiKeys.openaiApiKey.includes('*')) {
        try {
          const result = await validateOpenAIKey(apiKeys.openaiApiKey);

          if (result.success) {
            setValidationStatus('success');
            toast.success('OpenAI API key is valid and has been saved');

            // Update the API key in the state with the masked version
            if (result.data && result.data.openaiApiKey) {
              setApiKeys(prev => ({
                ...prev,
                openaiApiKey: result.data.openaiApiKey
              }));
            }

            // If valid, fetch available models and show toast
            fetchOpenAIModels(true);
          } else {
            setValidationStatus('error');
            toast.error('OpenAI API key is invalid');
          }
        } catch (error) {
          setValidationStatus('error');
          toast.error('OpenAI API key is invalid');
        }
      } else {
        toast.error('Please enter a valid API key to validate');
        setValidationStatus('error');
      }
    } catch (error) {
      toast.error('Failed to validate API key');
      setValidationStatus('error');
      console.error('Error validating API key:', error);
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
      </div>

      {isLoading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="space-y-6">

<Card>
            <CardHeader>
              <CardTitle>Zoom API Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Client ID
                  </label>
                  <Input
                    name="zoomClientId"
                    value={apiKeys.zoomClientId}
                    onChange={handleInputChange}
                    placeholder="Zoom Client ID"
                    type="password"
                    icon={<Key size={18} />}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your Zoom Client ID from the Zoom Developer Portal.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Client Secret
                  </label>
                  <Input
                    name="zoomClientSecret"
                    value={apiKeys.zoomClientSecret}
                    onChange={handleInputChange}
                    placeholder="Zoom Client Secret"
                    type="password"
                    icon={<Key size={18} />}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your Zoom Client Secret from the Zoom Developer Portal.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Account ID
                  </label>
                  <Input
                    name="zoomAccountId"
                    value={apiKeys.zoomAccountId}
                    onChange={handleInputChange}
                    placeholder="Zoom Account ID"
                    type="password"
                    icon={<Key size={18} />}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your Zoom Account ID from the Zoom Developer Portal.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Contact Name
                  </label>
                  <Input
                    name="zoomContactName"
                    value={apiKeys.zoomContactName}
                    onChange={handleInputChange}
                    placeholder="Contact Name"
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Name to use for Zoom meeting invitations.
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    Contact Email
                  </label>
                  <Input
                    name="zoomContactEmail"
                    value={apiKeys.zoomContactEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    type="email"
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Email to use for Zoom meeting invitations.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>OpenAI Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    API Key
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-grow">
                      <Input
                        name="openaiApiKey"
                        value={apiKeys.openaiApiKey}
                        onChange={handleInputChange}
                        placeholder="sk-..."
                        type="password"
                        icon={<Key size={18} />}
                        className={validationStatus === 'error' ? 'border-red-500' : ''}
                      />
                    </div>
                    <Button
                      onClick={handleValidateOpenAI}
                      disabled={isValidating}
                      variant={validationStatus === 'success' ? 'primary' : validationStatus === 'error' ? 'danger' : 'primary'}
                    >
                      {isValidating ? (
                        <RefreshCw size={18} className="animate-spin mr-2" />
                      ) : validationStatus === 'success' ? (
                        <Check size={18} className="mr-2" />
                      ) : validationStatus === 'error' ? (
                        <AlertCircle size={18} className="mr-2" />
                      ) : null}
                      Validate
                    </Button>
                  </div>
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your OpenAI API key is stored securely and encrypted in the database.
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.openaiApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Model
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-grow">
                      <Select
                        name="openaiModel"
                        value={apiKeys.openaiModel}
                        onChange={handleInputChange}
                        disabled={!apiKeys.openaiApiKey}
                        className={!apiKeys.openaiApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                      >
                        {/* Default models */}
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-4-turbo">GPT-4 Turbo</option>

                        {/* Dynamically loaded models */}
                        {openaiModels.length > 0 && (
                          <>
                            <option disabled>──────────</option>
                            {openaiModels.map(model => (
                              <option key={model.id} value={model.id}>
                                {model.name}
                              </option>
                            ))}
                          </>
                        )}
                      </Select>
                    </div>
                    <Button
                      onClick={() => fetchOpenAIModels(true)}
                      disabled={isLoadingModels || !apiKeys.openaiApiKey}
                      variant="outline"
                    >
                      {isLoadingModels ? (
                        <RefreshCw size={18} className="animate-spin mr-2" />
                      ) : (
                        <RefreshCw size={18} className="mr-2" />
                      )}
                      Load Models
                    </Button>
                  </div>
                  <p className={`text-xs ${!apiKeys.openaiApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.openaiApiKey ? 'Enter an OpenAI API key to enable model selection.' : 'Select the OpenAI model to use for your IVR bot.'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Deepgram Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    API Key
                  </label>
                  <Input
                    name="deepgramApiKey"
                    value={apiKeys.deepgramApiKey}
                    onChange={handleInputChange}
                    placeholder="Deepgram API Key"
                    type="password"
                    icon={<Key size={18} />}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your Deepgram API key for speech-to-text functionality.
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Language
                  </label>
                  <Select
                    name="deepgramLanguage"
                    value={apiKeys.deepgramLanguage}
                    onChange={handleInputChange}
                    disabled={!apiKeys.deepgramApiKey}
                    className={!apiKeys.deepgramApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  >
                    {deepgramLanguages.map(language => (
                      <option key={language.code} value={language.code}>
                        {language.name} ({language.code})
                      </option>
                    ))}
                  </Select>
                  <p className={`text-xs ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.deepgramApiKey ? 'Enter a Deepgram API key to enable language selection.' : 'Select the language for speech recognition.'}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Utterance End (ms)
                  </label>
                  <Input
                    name="deepgramUtterance"
                    value={apiKeys.deepgramUtterance.toString()}
                    onChange={handleInputChange}
                    type="number"
                    min={1001}
                    max={5000}
                    disabled={!apiKeys.deepgramApiKey}
                    className={!apiKeys.deepgramApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  />
                  <p className={`text-xs ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.deepgramApiKey ? 'Enter a Deepgram API key to enable this setting.' : 'Time in milliseconds of silence to consider an utterance complete (1001-5000).'}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Interruption (ms)
                  </label>
                  <Input
                    name="deepgramInterruption"
                    value={apiKeys.deepgramInterruption.toString()}
                    onChange={handleInputChange}
                    type="number"
                    min={100}
                    max={1000}
                    disabled={!apiKeys.deepgramApiKey}
                    className={!apiKeys.deepgramApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  />
                  <p className={`text-xs ${!apiKeys.deepgramApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.deepgramApiKey ? 'Enter a Deepgram API key to enable this setting.' : 'Time in milliseconds to detect interruptions (100-1000).'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>ElevenLabs Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                    API Key
                  </label>
                  <Input
                    name="elevenlabsApiKey"
                    value={apiKeys.elevenlabsApiKey}
                    onChange={handleInputChange}
                    placeholder="ElevenLabs API Key"
                    type="password"
                    icon={<Key size={18} />}
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Your ElevenLabs API key for text-to-speech functionality.
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Model
                  </label>
                  <div className="flex space-x-2">
                    <div className="flex-grow">
                      <Select
                        name="elevenlabsModel"
                        value={apiKeys.elevenlabsModel}
                        onChange={handleInputChange}
                        disabled={!apiKeys.elevenlabsApiKey}
                        className={!apiKeys.elevenlabsApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                      >
                        {/* Default model */}
                        <option value="eleven_multilingual_v2">Eleven Multilingual v2</option>

                        {/* Dynamically loaded models */}
                        {elevenLabsModels.length > 0 && (
                          <>
                            <option disabled>──────────</option>
                            {elevenLabsModels.map(model => (
                              <option key={model.model_id} value={model.model_id}>
                                {model.name}
                              </option>
                            ))}
                          </>
                        )}
                      </Select>
                    </div>
                    <Button
                      onClick={() => fetchElevenLabsModels(true)}
                      disabled={isLoadingElevenLabsModels || !apiKeys.elevenlabsApiKey}
                      variant="outline"
                    >
                      {isLoadingElevenLabsModels ? (
                        <RefreshCw size={18} className="animate-spin mr-2" />
                      ) : (
                        <RefreshCw size={18} className="mr-2" />
                      )}
                      Load Models
                    </Button>
                  </div>
                  <p className={`text-xs ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.elevenlabsApiKey ? 'Enter an ElevenLabs API key to enable model selection.' : 'Select the ElevenLabs model to use for text-to-speech.'}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Stability ({apiKeys.elevenlabsStability.toFixed(2)})
                  </label>
                  <Input
                    name="elevenlabsStability"
                    value={apiKeys.elevenlabsStability}
                    onChange={handleInputChange}
                    type="range"
                    min={0}
                    max={1}
                    step={0.01}
                    disabled={!apiKeys.elevenlabsApiKey}
                    className={!apiKeys.elevenlabsApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  />
                  <p className={`text-xs ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.elevenlabsApiKey ? 'Enter an ElevenLabs API key to enable this setting.' : 'Higher values make the voice more consistent but may sound less natural (0-1).'}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Similarity ({apiKeys.elevenlabsSimilarity.toFixed(2)})
                  </label>
                  <Input
                    name="elevenlabsSimilarity"
                    value={apiKeys.elevenlabsSimilarity}
                    onChange={handleInputChange}
                    type="range"
                    min={0}
                    max={1}
                    step={0.01}
                    disabled={!apiKeys.elevenlabsApiKey}
                    className={!apiKeys.elevenlabsApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  />
                  <p className={`text-xs ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.elevenlabsApiKey ? 'Enter an ElevenLabs API key to enable this setting.' : 'Higher values make the voice sound more like the original voice (0-1).'}
                  </p>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-700 dark:text-slate-300'} mb-1`}>
                    Style Exaggeration ({apiKeys.elevenlabsExaggeration.toFixed(2)})
                  </label>
                  <Input
                    name="elevenlabsExaggeration"
                    value={apiKeys.elevenlabsExaggeration}
                    onChange={handleInputChange}
                    type="range"
                    min={0}
                    max={1}
                    step={0.01}
                    disabled={!apiKeys.elevenlabsApiKey}
                    className={!apiKeys.elevenlabsApiKey ? 'opacity-60 cursor-not-allowed' : ''}
                  />
                  <p className={`text-xs ${!apiKeys.elevenlabsApiKey ? 'text-slate-400 dark:text-slate-600' : 'text-slate-500 dark:text-slate-400'} mt-1`}>
                    {!apiKeys.elevenlabsApiKey ? 'Enter an ElevenLabs API key to enable this setting.' : 'Higher values increase the expressiveness of the voice (0-1).'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          

          <div className="flex justify-end">
            <Button
              onClick={handleSave}
              disabled={isSaving}
              size="lg"
            >
              {isSaving && <RefreshCw size={18} className="animate-spin mr-2" />}
              Save Changes
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApiKeysPage;


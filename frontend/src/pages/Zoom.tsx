import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import Textarea from '../components/ui/Textarea';
import { Video, Calendar, Clock, Link, Copy, ExternalLink, Check, AlertCircle, Phone, LogIn } from 'lucide-react';
import { createZoomMeeting, getZoomMeetingLogs, joinZoomMeeting } from '../services/zoomService';
import { ZoomMeetingResponse, ZoomMeetingLog } from '../types';
import toast from 'react-hot-toast';
import { format } from 'date-fns';

const Zoom: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [isJoining, setIsJoining] = useState(false);
  const [meetingLogs, setMeetingLogs] = useState<ZoomMeetingLog[]>([]);
  const [currentMeeting, setCurrentMeeting] = useState<ZoomMeetingResponse | null>(null);
  const [showMeetingDetails, setShowMeetingDetails] = useState(false);
  const [formData, setFormData] = useState({
    topic: '',
    agenda: '',
    start_time: '',
    duration: 60,
    join_with_agent: true,
  });
  const [joinFormData, setJoinFormData] = useState({
    meeting_id: '',
    passcode: '',
  });

  // Fetch meeting logs on component mount
  useEffect(() => {
    fetchMeetingLogs();
  }, []);

  const fetchMeetingLogs = async () => {
    try {
      const logs = await getZoomMeetingLogs();
      setMeetingLogs(logs);
    } catch (error) {
      console.error('Error fetching meeting logs:', error);
      // Don't show error toast here as the endpoint might not exist yet
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleJoinInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setJoinFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleJoinSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsJoining(true);

    try {
      // Validate required fields
      if (!joinFormData.meeting_id) {
        toast.error('Meeting ID is required');
        return;
      }

      // Join the meeting
      const response = await joinZoomMeeting({
        meeting_id: joinFormData.meeting_id,
        passcode: joinFormData.passcode || undefined
      });

      // Set the current meeting and show details
      setCurrentMeeting(response);
      setShowMeetingDetails(true);

      // Show appropriate message based on agent call status
      if (response.agent_call && response.agent_call.status === 'SUCCESS') {
        toast.success('AI agent joined the Zoom meeting successfully!');
      } else if (response.agent_call) {
        // Show warning if the agent call failed but we have meeting details
        const errorMessage = response.agent_call.error || 'Failed to connect to the meeting';
        toast.error(`Meeting found, but agent couldn't join: ${errorMessage}`);
      } else {
        toast.success('Meeting information retrieved successfully');
      }

      // Refresh meeting logs
      fetchMeetingLogs();

      // Reset form
      setJoinFormData({
        meeting_id: '',
        passcode: ''
      });
    } catch (error: any) {
      toast.error(error.message || 'Failed to join Zoom meeting');
    } finally {
      setIsJoining(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Format the meeting details
      const meetingDetails = {
        ...formData,
        // Convert start_time to ISO format if provided
        start_time: formData.start_time ? new Date(formData.start_time).toISOString() : undefined,
        // Convert duration to number
        duration: Number(formData.duration),
      };

      // Create the meeting
      const response = await createZoomMeeting(meetingDetails);

      // Set the current meeting and show details
      setCurrentMeeting(response);
      setShowMeetingDetails(true);

      // Show success message
      toast.success('Zoom meeting created successfully!');

      // Refresh meeting logs
      fetchMeetingLogs();

      // Reset form
      setFormData({
        topic: '',
        agenda: '',
        start_time: '',
        duration: 60,
        join_with_agent: true,
      });
    } catch (error: any) {
      toast.error(error.message || 'Failed to create Zoom meeting');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text)
      .then(() => toast.success(message))
      .catch(() => toast.error('Failed to copy to clipboard'));
  };

  const formatDateTime = (dateTimeString: string) => {
    try {
      return format(new Date(dateTimeString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return dateTimeString;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Create Meeting Card */}
        <Card>
          <CardHeader>
            <CardTitle>Create Zoom Meeting</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Input
                  label="Meeting Topic"
                  name="topic"
                  value={formData.topic}
                  onChange={handleInputChange}
                  placeholder="Enter meeting topic"
                  required
                />
              </div>

              <div>
                <Textarea
                  label="Meeting Agenda (Optional)"
                  name="agenda"
                  value={formData.agenda}
                  onChange={handleInputChange}
                  placeholder="Enter meeting agenda"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Input
                    label="Start Time (Optional)"
                    name="start_time"
                    type="datetime-local"
                    value={formData.start_time}
                    onChange={handleInputChange}
                    placeholder="Select start time"
                  />
                  <p className="text-xs text-slate-500 mt-1">
                    If not specified, meeting will start 5 minutes from now
                  </p>
                </div>

                <div>
                  <Input
                    label="Duration (minutes)"
                    name="duration"
                    type="number"
                    value={formData.duration.toString()}
                    onChange={handleInputChange}
                    placeholder="Enter duration in minutes"
                    min="15"
                    max="240"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2 mt-2">
                <input
                  type="checkbox"
                  id="join_with_agent"
                  name="join_with_agent"
                  checked={formData.join_with_agent}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label htmlFor="join_with_agent" className="text-sm text-slate-700 dark:text-slate-300">
                  Join meeting with AI Voice Agent
                </label>
              </div>

              <div className="mt-4">
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={isLoading}
                  icon={<Video size={16} />}
                  className="w-full md:w-auto"
                >
                  Create Zoom Meeting
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Join Meeting Card */}
        <Card>
          <CardHeader>
            <CardTitle>Join Existing Meeting</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleJoinSubmit} className="space-y-4">
              <div>
                <Input
                  label="Meeting ID"
                  name="meeting_id"
                  value={joinFormData.meeting_id}
                  onChange={handleJoinInputChange}
                  placeholder="Enter Zoom meeting ID"
                  required
                />
                <p className="text-xs text-slate-500 mt-1">
                  The ID of the Zoom meeting you want to join
                </p>
              </div>

              <div>
                <Input
                  label="Passcode (Optional)"
                  name="passcode"
                  value={joinFormData.passcode}
                  onChange={handleJoinInputChange}
                  placeholder="Enter meeting passcode"
                  type="password"
                />
                <p className="text-xs text-slate-500 mt-1">
                  The passcode for the Zoom meeting (if required)
                </p>
              </div>

              <div className="mt-4">
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={isJoining}
                  icon={<LogIn size={16} />}
                  className="w-full md:w-auto"
                >
                  Join with AI Agent
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* Meeting Details Card (shown after creating a meeting) */}
      {showMeetingDetails && currentMeeting && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Meeting Details</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowMeetingDetails(false)}
              >
                Close
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Video className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Meeting Topic</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">
                    {currentMeeting.meeting.topic}
                  </p>
                </div>

                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Start Time</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">
                    {formatDateTime(currentMeeting.meeting.start_time)}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Duration</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">
                    {currentMeeting.meeting.duration} minutes
                  </p>
                </div>

                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Link className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Meeting ID</h3>
                  </div>
                  <div className="flex items-center">
                    <p className="text-lg font-semibold text-slate-700 dark:text-slate-200 mr-2">
                      {currentMeeting.meeting.id}
                    </p>
                    <button
                      onClick={() => copyToClipboard(currentMeeting.meeting.id, 'Meeting ID copied to clipboard')}
                      className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                      title="Copy Meeting ID"
                    >
                      <Copy size={16} />
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center">
                    <ExternalLink className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Join URL</h3>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => copyToClipboard(currentMeeting.meeting.join_url, 'Join URL copied to clipboard')}
                      className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                      title="Copy Join URL"
                    >
                      <Copy size={16} />
                    </button>
                    <a
                      href={currentMeeting.meeting.join_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                      title="Open in new tab"
                    >
                      <ExternalLink size={16} />
                    </a>
                  </div>
                </div>
                <p className="text-sm text-slate-700 dark:text-slate-200 break-all">
                  {currentMeeting.meeting.join_url}
                </p>
              </div>

              {currentMeeting.sip && currentMeeting.sip.uri && (
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                      <h3 className="font-medium text-slate-900 dark:text-white">SIP URI</h3>
                    </div>
                    <button
                      onClick={() => copyToClipboard(currentMeeting.sip.uri || '', 'SIP URI copied to clipboard')}
                      className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                      title="Copy SIP URI"
                    >
                      <Copy size={16} />
                    </button>
                  </div>
                  <p className="text-sm text-slate-700 dark:text-slate-200 break-all">
                    {currentMeeting.sip.uri}
                  </p>
                </div>
              )}

              {currentMeeting.agent_call && (
                <div className={`bg-slate-50 dark:bg-slate-700 p-4 rounded-lg ${
                  currentMeeting.agent_call.status === 'SUCCESS'
                    ? 'border-l-4 border-green-500'
                    : 'border-l-4 border-red-500'
                }`}>
                  <div className="flex items-center mb-2">
                    {currentMeeting.agent_call.status === 'SUCCESS' ? (
                      <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
                    )}
                    <h3 className="font-medium text-slate-900 dark:text-white">AI Agent Status</h3>
                  </div>
                  <p className="text-sm text-slate-700 dark:text-slate-200">
                    {currentMeeting.agent_call.status === 'SUCCESS'
                      ? 'AI Voice Agent successfully joined the meeting.'
                      : currentMeeting.agent_call.status === 'INCOMPATIBLE_DESTINATION'
                        ? 'Failed to join meeting: Incompatible destination. This may be due to SIP configuration issues with Zoom.'
                        : `Failed to join meeting: ${currentMeeting.agent_call.error || currentMeeting.agent_call.status || 'Unknown error'}`
                    }
                  </p>
                  {currentMeeting.agent_call.status !== 'SUCCESS' && (
                    <div className="mt-2 text-xs text-slate-500 bg-slate-100 dark:bg-slate-800 p-2 rounded">
                      <p className="font-semibold">Troubleshooting tips:</p>
                      <ul className="list-disc pl-4 mt-1">
                        <li>Verify that the meeting has SIP enabled in Zoom settings</li>
                        <li>Check that the meeting is currently active</li>
                        <li>Ensure the passcode is correct (if required)</li>
                        <li>Try creating a new meeting instead of joining an existing one</li>
                      </ul>
                    </div>
                  )}
                  {currentMeeting.agent_call.uuid && (
                    <p className="text-xs text-slate-500 mt-1">
                      Call UUID: {currentMeeting.agent_call.uuid}
                    </p>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Meeting Logs Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Meeting Logs</CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchMeetingLogs}
              icon={<Calendar size={14} />}
            >
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {meetingLogs.length === 0 ? (
            <div className="text-center py-8 text-slate-500 dark:text-slate-400">
              <Video size={48} className="mx-auto mb-4 opacity-20" />
              <p>No meeting logs found. Create your first Zoom meeting!</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="text-left border-b border-slate-200 dark:border-slate-700">
                    <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Topic</th>
                    <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Start Time</th>
                    <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Duration</th>
                    <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Agent Status</th>
                    <th className="px-4 py-3 text-slate-500 dark:text-slate-400 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {meetingLogs.map((log) => (
                    <tr key={log._id} className="border-b border-slate-200 dark:border-slate-700">
                      <td className="px-4 py-3 text-slate-700 dark:text-slate-300">
                        {log.meeting.topic}
                      </td>
                      <td className="px-4 py-3 text-slate-700 dark:text-slate-300">
                        {formatDateTime(log.meeting.start_time)}
                      </td>
                      <td className="px-4 py-3 text-slate-700 dark:text-slate-300">
                        {log.meeting.duration} min
                      </td>
                      <td className="px-4 py-3 text-slate-700 dark:text-slate-300">
                        {log.agent_call ? (
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            log.agent_call.status === 'SUCCESS'
                              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                          }`}>
                            {log.agent_call.status === 'SUCCESS' ? (
                              <>
                                <Check size={12} className="mr-1" />
                                Joined
                              </>
                            ) : (
                              <>
                                <AlertCircle size={12} className="mr-1" />
                                Failed
                              </>
                            )}
                          </span>
                        ) : (
                          <span className="text-slate-500 dark:text-slate-400">N/A</span>
                        )}
                      </td>
                      <td className="px-4 py-3 text-slate-700 dark:text-slate-300">
                        <div className="flex items-center space-x-2">
                          <a
                            href={log.meeting.join_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                            title="Join Meeting"
                          >
                            <ExternalLink size={16} />
                          </a>
                          <button
                            onClick={() => copyToClipboard(log.meeting.join_url, 'Join URL copied to clipboard')}
                            className="p-1 text-slate-500 hover:text-indigo-600 dark:text-slate-400 dark:hover:text-indigo-400"
                            title="Copy Join URL"
                          >
                            <Copy size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Zoom;

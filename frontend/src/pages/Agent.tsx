import React, { useState, useEffect } from 'react';
import Card, { <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from '../components/ui/Card';
import Button from '../components/ui/Button';
import Textarea from '../components/ui/Textarea';
import Input from '../components/ui/Input';
import { Mic, Play, Pause, Check, Save, RefreshCw, Plus, AlertCircle } from 'lucide-react';
import { Voice, Prompt } from '../types';
import { getActiveVoice, activateElevenLabsVoice, activateCustomVoiceId } from '../services/voiceService';
import { getPrompts, updatePrompt, createPrompt } from '../services/promptService';
import { getElevenLabsVoices, playVoicePreview, verifyElevenLabsVoiceId, ElevenLabsVoice } from '../services/elevenLabsService';
import toast from 'react-hot-toast';

const Agent: React.FC = () => {
  // State for prompt
  const [prompt, setPrompt] = useState<Prompt | null>(null);
  const [promptContent, setPromptContent] = useState('');
  const [isPromptLoading, setIsPromptLoading] = useState(false);

  // State for voices
  const [activeVoice, setActiveVoice] = useState<Voice | null>(null);
  const [elevenLabsVoices, setElevenLabsVoices] = useState<ElevenLabsVoice[]>([]);
  const [isVoicesLoading, setIsVoicesLoading] = useState(false);
  const [isElevenLabsLoading, setIsElevenLabsLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState<string | null>(null);

  // State for custom voice ID
  const [customVoiceId, setCustomVoiceId] = useState('');
  const [isVerifyingVoice, setIsVerifyingVoice] = useState(false);
  const [verifiedVoice, setVerifiedVoice] = useState<ElevenLabsVoice | null>(null);

  useEffect(() => {
    fetchPrompt();
    fetchActiveVoice();
    fetchElevenLabsVoices();
  }, []);

  // Fetch the prompt (only one prompt now)
  const fetchPrompt = async () => {
    setIsPromptLoading(true);
    try {
      const prompts = await getPrompts();
      // Get the first prompt or null if none exists
      const agentPrompt = prompts.find(p => p.type === 'agent') || null;
      setPrompt(agentPrompt);
      if (agentPrompt) {
        try {
          // Try to parse the content as JSON
          const parsedContent = JSON.parse(agentPrompt.content);
          setPromptContent(parsedContent.prompt || '');
        } catch (parseError) {
          // If parsing fails, assume it's just a prompt without JSON structure
          setPromptContent(agentPrompt.content);
          console.warn('Content is not in JSON format, using as prompt only');
        }
      }
    } catch (error) {
      toast.error('Failed to fetch prompt');
      console.error('Error fetching prompt:', error);
    } finally {
      setIsPromptLoading(false);
    }
  };

  // Fetch active voice from database
  const fetchActiveVoice = async () => {
    setIsVoicesLoading(true);
    try {
      const voice = await getActiveVoice();
      setActiveVoice(voice);
    } catch (error) {
      toast.error('Failed to fetch active voice');
      console.error('Error fetching active voice:', error);
      setActiveVoice(null);
    } finally {
      setIsVoicesLoading(false);
    }
  };

  // Fetch voices from ElevenLabs API
  const fetchElevenLabsVoices = async () => {
    setIsElevenLabsLoading(true);
    try {
      const data = await getElevenLabsVoices();
      setElevenLabsVoices(data || []);
    } catch (error) {
      toast.error('Failed to fetch ElevenLabs voices');
      console.error('Error fetching ElevenLabs voices:', error);
      setElevenLabsVoices([]);
    } finally {
      setIsElevenLabsLoading(false);
    }
  };

  // Save the prompt
  const handleSavePrompt = async () => {
    setIsPromptLoading(true);
    try {
      // Create a JSON object with just the prompt
      const contentObject = {
        prompt: promptContent
      };

      // Convert to JSON string
      const jsonContent = JSON.stringify(contentObject);

      if (prompt) {
        // Update existing prompt
        await updatePrompt(prompt._id, {
          content: jsonContent,
          name: prompt.name,
          description: prompt.description || ''
        });
        toast.success('Prompt updated successfully');
      } else {
        // Create new prompt
        const newPrompt = await createPrompt({
          name: 'AI Voice Translator',
          type: 'agent',
          content: jsonContent,
          description: 'AI Voice Translator prompt for real-time translation between languages'
        });
        setPrompt(newPrompt);
        toast.success('Prompt created successfully');
      }
    } catch (error) {
      toast.error('Failed to save prompt');
      console.error('Error saving prompt:', error);
    } finally {
      setIsPromptLoading(false);
    }
  };



  // Handle play preview
  const handlePlayPreview = async (voiceId: string, previewUrl?: string) => {
    if (!previewUrl) return;

    if (isPlaying === voiceId) {
      // Stop playing
      setIsPlaying(null);
    } else {
      // Start playing
      setIsPlaying(voiceId);

      try {
        // Play the audio using the service function
        await playVoicePreview(previewUrl);
        // When audio finishes playing, this will execute
        setIsPlaying(null);
      } catch (error) {
        console.error('Error playing audio:', error);
        toast.error('Failed to play audio preview');
        setIsPlaying(null);
      }
    }
  };

  // Activate an ElevenLabs voice
  const handleActivateVoice = async (elevenLabsVoice: ElevenLabsVoice) => {
    setIsVoicesLoading(true);
    try {
      const updatedVoice = await activateElevenLabsVoice(elevenLabsVoice);
      setActiveVoice(updatedVoice);
      toast.success(`Voice "${elevenLabsVoice.name}" activated successfully`);
    } catch (error) {
      toast.error('Failed to activate voice');
      console.error('Error activating voice:', error);
    } finally {
      setIsVoicesLoading(false);
    }
  };

  // Verify a custom ElevenLabs voice ID
  const handleVerifyVoiceId = async () => {
    if (!customVoiceId.trim()) {
      toast.error('Please enter a voice ID');
      return;
    }

    setIsVerifyingVoice(true);
    setVerifiedVoice(null);

    try {
      const voice = await verifyElevenLabsVoiceId(customVoiceId.trim());

      // If the voice doesn't have a name, use a default
      if (!voice.name) {
        voice.name = 'Custom Voice';
      }

      setVerifiedVoice(voice);
      toast.success(`Voice ID verified successfully: ${voice.name}`);
    } catch (error) {
      toast.error('Failed to verify voice ID. Please check if the ID is correct.');
      console.error('Error verifying voice ID:', error);
    } finally {
      setIsVerifyingVoice(false);
    }
  };

  // Activate a custom voice ID
  const handleActivateCustomVoice = async () => {
    if (!verifiedVoice) {
      toast.error('Please verify a voice ID first');
      return;
    }

    setIsVoicesLoading(true);
    try {
      const updatedVoice = await activateCustomVoiceId(
        verifiedVoice.voice_id,
        verifiedVoice.name,
        verifiedVoice.description,
        verifiedVoice.preview_url
      );

      setActiveVoice(updatedVoice);
      toast.success(`Custom voice "${verifiedVoice.name}" activated successfully`);

      // Reset the form
      setCustomVoiceId('');
      setVerifiedVoice(null);
    } catch (error) {
      toast.error('Failed to activate custom voice');
      console.error('Error activating custom voice:', error);
    } finally {
      setIsVoicesLoading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Prompt Section - Takes 2/3 of the space */}
      <div className="md:col-span-2">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>AI Voice Translator Prompt</CardTitle>
              <Button
                variant="primary"
                onClick={handleSavePrompt}
                isLoading={isPromptLoading}
                icon={<Save size={18} />}
                disabled={!promptContent}
              >
                Save
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">


              <div>
                <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                  Translator Prompt
                </label>
                <Textarea
                  value={promptContent}
                  onChange={(e) => setPromptContent(e.target.value)}
                  className="min-h-[400px]"
                  placeholder="Enter the translator prompt content that defines how the AI translates between languages"
                />
              </div>
            </div>

            {prompt && (
              <div className="mt-4 text-sm text-slate-500 flex justify-between">
                <span>Created: {new Date(prompt.createdAt).toLocaleString()}</span>
                <span>Last updated: {new Date(prompt.updatedAt).toLocaleString()}</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Voices Section - Takes 1/3 of the space */}
      <div>
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Voices</CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchElevenLabsVoices}
                icon={<RefreshCw size={14} />}
                isLoading={isElevenLabsLoading}
              >
                Refresh
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {isVoicesLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <>
                {/* Active Voice Section */}
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-3">Active Voice</h3>
                  {activeVoice ? (
                    <div className="space-y-3">
                      <div
                        key={activeVoice._id}
                        className="flex items-center justify-between p-3 rounded-lg border border-slate-200 dark:border-slate-700"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900">
                            <Mic className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <div className="flex items-center">
                              <h3 className="font-medium text-slate-900 dark:text-white">
                                {activeVoice.name}
                              </h3>
                              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                <Check size={10} className="mr-1" /> Active
                              </span>
                            </div>
                            <p className="text-xs text-slate-500">
                              ID: {activeVoice.voiceId}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {activeVoice.previewUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePlayPreview(activeVoice._id, activeVoice.previewUrl)}
                              icon={isPlaying === activeVoice._id ? <Pause size={14} /> : <Play size={14} />}
                            >
                              {isPlaying === activeVoice._id ? 'Stop' : 'Play'}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <p className="text-slate-500 dark:text-slate-400">
                        No active voice set
                      </p>
                    </div>
                  )}
                </div>

                {/* Custom Voice ID Section */}
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-3">Custom Voice ID</h3>
                  <div className="space-y-3 p-4 rounded-lg border border-slate-200 dark:border-slate-700">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                        ElevenLabs Voice ID
                      </label>
                      <div className="flex space-x-2">
                        <Input
                          value={customVoiceId}
                          onChange={(e) => setCustomVoiceId(e.target.value)}
                          placeholder="Enter ElevenLabs voice ID"
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleVerifyVoiceId}
                          isLoading={isVerifyingVoice}
                        >
                          Verify
                        </Button>
                      </div>
                    </div>

                    {verifiedVoice && (
                      <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-900">
                        <div className="flex items-center space-x-2 mb-2">
                          <Check className="h-5 w-5 text-green-600 dark:text-green-400" />
                          <h4 className="font-medium text-green-800 dark:text-green-400">Voice Verified</h4>
                        </div>
                        <p className="text-sm text-green-700 dark:text-green-300 mb-3">
                          Voice ID: {verifiedVoice.voice_id}
                          {verifiedVoice.name && <span> - {verifiedVoice.name}</span>}
                        </p>
                        <Button
                          variant="primary"
                          size="sm"
                          onClick={handleActivateCustomVoice}
                          className="w-full"
                        >
                          Activate This Voice
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* ElevenLabs Voices Section */}
                <div>
                  <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-3">ElevenLabs Voices</h3>
                  {isElevenLabsLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    </div>
                  ) : elevenLabsVoices.length > 0 ? (
                    <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
                      {elevenLabsVoices.map((voice) => (
                        <div
                          key={voice.voice_id}
                          className="flex items-center justify-between p-3 rounded-lg border border-slate-200 dark:border-slate-700"
                        >
                          <div className="flex items-center space-x-3">
                            <div className="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-900">
                              <Mic className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <div>
                              <h3 className="font-medium text-slate-900 dark:text-white">
                                {voice.name}
                              </h3>
                              {voice.labels && (
                                <p className="text-xs text-slate-500 dark:text-slate-400">
                                  {voice.labels.gender} {voice.labels.accent} {voice.labels.age}
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {voice.preview_url && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePlayPreview(voice.voice_id, voice.preview_url)}
                                icon={isPlaying === voice.voice_id ? <Pause size={14} /> : <Play size={14} />}
                              >
                                {isPlaying === voice.voice_id ? 'Stop' : 'Play'}
                              </Button>
                            )}
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleActivateVoice(voice)}
                              icon={<Check size={14} />}
                            >
                              Activate
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <p className="text-slate-500 dark:text-slate-400">
                        No ElevenLabs voices found. Please check your API key in settings.
                      </p>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Agent;

import React, { useState } from 'react';
import SignIn from './SignIn';
import SignUp from './SignUp';

const AuthPage: React.FC = () => {
  const [isSignIn, setIsSignIn] = useState(true);
  
  return (
    <div className="min-h-screen flex items-center justify-center px-4 bg-slate-50 dark:bg-slate-950">
      <div className="w-full max-w-md">
        {isSignIn ? (
          <SignIn onSignUp={() => setIsSignIn(false)} />
        ) : (
          <SignUp onSignIn={() => setIsSignIn(true)} />
        )}
      </div>
    </div>
  );
};

export default AuthPage;
import React, { useState, useEffect } from 'react';
import { CallHistory } from '../types';
import { getCallHistoryEntry } from '../services/callHistoryService';
import { getRecording, downloadRecording } from '../services/recordingService';
import { Phone, Clock, Calendar, ArrowRight, FileText, X, Download, FileAudio } from 'lucide-react';
import Button from './ui/Button';
import toast from 'react-hot-toast';

const API_URL = import.meta.env.VITE_API_URL;

interface CallHistoryDetailModalProps {
  callId: string;
  onClose: () => void;
}

const CallHistoryDetailModal: React.FC<CallHistoryDetailModalProps> = ({ callId, onClose }) => {
  const [call, setCall] = useState<CallHistory | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCallDetails = async () => {
      try {
        setIsLoading(true);
        // Get basic call details
        const callData = await getCallHistoryEntry(callId);

        // Try to get recording information if the call is completed
        try {
          const recordingData = await getRecording(callId);

          // Merge recording data with call data
          setCall({
            ...callData,
            recordingUrl: recordingData.streamUrl,
            downloadUrl: recordingData.downloadUrl,
            fileSize: recordingData.fileSize
          });

          console.log('Recording data loaded:', recordingData);
        } catch (recordingError) {
          // If recording not found, just use the call data
          console.log('Recording not available:', recordingError);
          setCall(callData);
        }
      } catch (error) {
        toast.error('Failed to load call details');
        console.error('Error fetching call details:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (callId) {
      fetchCallDetails();
    }
  }, [callId]);

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // Format file size for display
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  // Handle recording download
  const handleDownload = () => {
    if (call?._id) {
      try {
        // Use the downloadRecording helper function to trigger file download
        downloadRecording(call._id);
        toast.success('Downloading recording...');

        // Log the download attempt
        console.log('Initiated download for recording ID:', call._id);
      } catch (error) {
        toast.error('Failed to download recording');
        console.error('Error downloading recording:', error);
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-slate-900 dark:text-white">Call Details</h2>
            <button
              onClick={onClose}
              className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
            >
              <X size={24} />
            </button>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : call ? (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Phone className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Phone Number</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">{call.phoneNumber}</p>
                </div>

                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Duration</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">{call.formattedDuration}</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Start Time</h3>
                  </div>
                  <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">{formatDate(call.callStartTime)}</p>
                </div>

                {call.callEndTime && (
                  <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                      <h3 className="font-medium text-slate-900 dark:text-white">End Time</h3>
                    </div>
                    <p className="text-lg font-semibold text-slate-700 dark:text-slate-200">{formatDate(call.callEndTime)}</p>
                  </div>
                )}
              </div>

              {/* Recording section */}
              {call._id && (
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <FileAudio className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                      <h3 className="font-medium text-slate-900 dark:text-white">Call Recording</h3>
                    </div>
                    <div className="text-sm text-slate-500 dark:text-slate-400">
                      {call.fileSize ? formatFileSize(call.fileSize) : ''}
                    </div>
                  </div>

                  <div className="space-y-3">
                    {/* Audio player */}
                    <audio
                      controls
                      className="w-full"
                      key={`audio-player-${call._id}`} // Add key to force re-render when call changes
                      src={`${API_URL}/api/recordings/${call._id}/stream?token=${localStorage.getItem('token')}`}
                      preload="metadata"
                      onLoadStart={() => {
                        const audioUrl = `${API_URL}/api/recordings/${call._id}/stream?token=${localStorage.getItem('token')}`;
                        console.log('Audio loading from:', audioUrl);
                      }}
                      onCanPlay={() => console.log('Audio can play now')}
                      onError={(e) => {
                        console.error('Audio failed to load:', e);
                        const audioUrl = `${API_URL}/api/recordings/${call._id}/stream?token=${localStorage.getItem('token')}`;
                        console.error('Audio URL was:', audioUrl);
                        toast.error('Audio failed to load. The recording may not exist.');
                      }}
                    >
                      Your browser does not support the audio element.
                    </audio>

                    {/* Download button */}
                    <div className="flex justify-end">
                      <Button
                        variant="primary"
                        onClick={handleDownload}
                        icon={<Download size={16} />}
                        size="sm"
                      >
                        Download Recording
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {call.transcript && (
                <div className="bg-slate-50 dark:bg-slate-700 p-4 rounded-lg">
                  <div className="flex items-center mb-2">
                    <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                    <h3 className="font-medium text-slate-900 dark:text-white">Transcript</h3>
                  </div>
                  <div className="max-h-60 overflow-y-auto bg-white dark:bg-slate-800 p-3 rounded border border-slate-200 dark:border-slate-600">
                    <p className="whitespace-pre-line text-slate-700 dark:text-slate-200">{call.transcript}</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8 text-slate-500">
              <p>Call details not found</p>
            </div>
          )}

          <div className="mt-8 flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallHistoryDetailModal;

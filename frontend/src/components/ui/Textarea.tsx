import React, { forwardRef } from 'react';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  fullWidth?: boolean;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ label, error, fullWidth = true, className = '', ...props }, ref) => {
    const textareaStyles = `
      block px-4 py-2 w-full rounded-md border border-slate-300 
      bg-white text-slate-900 placeholder:text-slate-400
      focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent
      dark:bg-slate-800 dark:border-slate-700 dark:text-slate-100
      disabled:opacity-50 disabled:cursor-not-allowed min-h-[100px]
      ${error ? 'border-red-500 focus:ring-red-500' : ''}
      ${className}
    `;

    return (
      <div className={`${fullWidth ? 'w-full' : ''} space-y-1`}>
        {label && (
          <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
            {label}
          </label>
        )}
        <textarea ref={ref} className={textareaStyles} {...props} />
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

export default Textarea;
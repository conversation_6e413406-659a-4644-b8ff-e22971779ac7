import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Mic, Settings, Key, Bot, FileText, Video } from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  activePath: string;
}

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, activePath }) => {
  const location = useLocation();

  const navItems: NavItem[] = [
    { path: '/', label: 'Dashboard', icon: <Home size={20} /> },
    { path: '/agent', label: 'Agent', icon: <Bot size={20} /> },
    // { path: '/zoom', label: 'Zoom Meetings', icon: <Video size={20} /> },
    { path: '/api-keys', label: 'API Keys', icon: <Key size={20} /> },
    { path: '/settings', label: 'Settings', icon: <Settings size={20} /> },
  ];

  return (
    <aside
      className={`fixed inset-y-0 left-0 z-40 w-64 transform bg-slate-50 dark:bg-slate-900 border-r border-slate-200 dark:border-slate-800 transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto lg:h-screen ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}
    >
      <div className="h-full flex flex-col overflow-y-auto">
        <div className="h-16 flex items-center border-b border-slate-200 dark:border-slate-800 px-6">
          <div className="flex items-center gap-2">
            <div className="bg-indigo-600 text-white p-2 rounded">
              <Mic size={20} />
            </div>
            <span className="text-xl font-semibold text-slate-900 dark:text-white">
              AI Voice Translator
            </span>
          </div>
        </div>
        <nav className="flex-1 px-3 py-4">
          <ul className="space-y-1">
            {navItems.map((item) => (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={`flex items-center w-full rounded-md px-3 py-2 text-sm font-medium transition-colors duration-200 ${
                    location.pathname === item.path
                      ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/50 dark:text-indigo-300'
                      : 'text-slate-700 hover:bg-slate-100 dark:text-slate-300 dark:hover:bg-slate-800'
                  }`}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
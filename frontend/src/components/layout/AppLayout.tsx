import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useAuth } from '../../contexts/AuthContext';

const AppLayout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { authState } = useAuth();
  const location = useLocation();
  const [title, setTitle] = useState('');

  // Set page title based on current path
  useEffect(() => {
    const path = location.pathname;
    let pageTitle = '';

    switch (path) {
      case '/':
        pageTitle = 'Dashboard';
        document.title = 'Dashboard | AI Voice Translator';
        break;
      case '/agent':
        pageTitle = 'Agent';
        document.title = 'Agent | AI Voice Translator';
        break;
      case '/knowledge-base':
        pageTitle = 'Knowledge Base';
        document.title = 'Knowledge Base | AI Voice Translator';
        break;
      case '/zoom':
        pageTitle = 'Zoom Meetings';
        document.title = 'Zoom Meetings | AI Voice Translator';
        break;
      case '/settings':
        pageTitle = 'Settings';
        document.title = 'Settings | AI Voice Translator';
        break;
      case '/api-keys':
        pageTitle = 'API Keys';
        document.title = 'API Keys | AI Voice Translator';
        break;

      default:
        pageTitle = '';
        document.title = 'AI Voice Translator';
    }

    setTitle(pageTitle);
  }, [location.pathname]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  if (!authState.isAuthenticated) {
    return <Outlet />;
  }

  return (
    <div className="flex h-screen bg-slate-100 dark:bg-slate-950 transition-colors duration-200">
      <Sidebar
        isOpen={isSidebarOpen}
        activePath={location.pathname}
      />

      <div className="flex flex-col flex-1 w-full overflow-hidden">
        <Header
          toggleSidebar={toggleSidebar}
          isSidebarOpen={isSidebarOpen}
        />

        <main className="flex-1 overflow-y-auto p-4 sm:p-6">
          {title && (
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-slate-900 dark:text-white">
                {title}
              </h1>
            </div>
          )}
          <Outlet />
        </main>
      </div>

      {/* Overlay for mobile sidebar */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={toggleSidebar}
          aria-hidden="true"
        />
      )}
    </div>
  );
};

export default AppLayout;
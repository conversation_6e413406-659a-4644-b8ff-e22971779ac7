import { createContext, useContext, useState, useEffect } from 'react';
import { Apps } from '@zoom/appssdk';

// Create context
const ZoomContext = createContext(null);

// Custom hook to use the Zoom context
export const useZoom = () => {
  const context = useContext(ZoomContext);
  if (!context) {
    throw new Error('useZoom must be used within a ZoomProvider');
  }
  return context;
};

// Provider component
export const ZoomProvider = ({ children }) => {
  const [client, setClient] = useState(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [meetingContext, setMeetingContext] = useState({
    meetingId: null,
    participantId: null,
    userName: null,
    role: null,
    inMeeting: false,
  });

  // Initialize Zoom SDK
  useEffect(() => {
    const initializeZoomSDK = async () => {
      try {
        // Initialize Zoom Apps SDK
        const configResponse = await Apps.configure({
          capabilities: [
            // Add required capabilities
            { name: 'getMeetingContext' },
            { name: 'getRunningContext' },
            { name: 'authorize' },
          ],
          version: '0.16.0',
        });

        console.log('Zoom Apps SDK initialized:', configResponse);
        setClient(Apps);
        setIsInitialized(true);

        // Get meeting context
        await fetchMeetingContext();
      } catch (err) {
        console.error('Failed to initialize Zoom SDK:', err);
        setError(err.message || 'Failed to initialize Zoom SDK');
      }
    };

    initializeZoomSDK();
  }, []);

  // Fetch meeting context
  const fetchMeetingContext = async () => {
    if (!client) return;

    try {
      const context = await client.getMeetingContext();
      const runningContext = await client.getRunningContext();
      
      setMeetingContext({
        meetingId: context.meetingId,
        participantId: context.participantId,
        userName: context.userName,
        role: context.role,
        inMeeting: runningContext.runningContext === 'inMeeting',
      });
      
      console.log('Meeting context:', context);
      console.log('Running context:', runningContext);
    } catch (err) {
      console.error('Failed to get meeting context:', err);
      setError(err.message || 'Failed to get meeting context');
    }
  };

  // Value to be provided by the context
  const value = {
    client,
    isInitialized,
    error,
    meetingContext,
    refreshMeetingContext: fetchMeetingContext,
  };

  return <ZoomContext.Provider value={value}>{children}</ZoomContext.Provider>;
};

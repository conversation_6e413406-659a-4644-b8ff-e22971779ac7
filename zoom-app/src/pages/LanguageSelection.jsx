import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useZoom } from '../contexts/ZoomContext';
import apiService from '../services/apiService';
import toast from 'react-hot-toast';

const LanguageSelection = () => {
  const { meetingContext } = useZoom();
  const [languages, setLanguages] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchLanguages = async () => {
      try {
        setIsLoading(true);
        // In a real app, this would fetch from your API
        // For now, we'll use mock data
        const mockLanguages = [
          { code: 'en-US', name: 'English (US)' },
          { code: 'es-ES', name: 'Spanish (Spain)' },
          { code: 'fr-FR', name: 'French (France)' },
          { code: 'de-DE', name: 'German (Germany)' },
          { code: 'it-IT', name: 'Italian (Italy)' },
          { code: 'ja-JP', name: 'Japanese (Japan)' },
          { code: 'ko-KR', name: 'Korean (South Korea)' },
          { code: 'pt-BR', name: 'Portuguese (Brazil)' },
          { code: 'zh-CN', name: 'Chinese (Simplified)' },
          { code: 'ru-RU', name: 'Russian (Russia)' },
        ];
        
        // Simulate API call
        setTimeout(() => {
          setLanguages(mockLanguages);
          setIsLoading(false);
        }, 500);
        
        // In a real app, you would use:
        // const response = await apiService.getLanguages();
        // setLanguages(response);
      } catch (error) {
        console.error('Failed to fetch languages:', error);
        toast.error('Failed to load available languages');
        setIsLoading(false);
      }
    };

    fetchLanguages();
  }, []);

  const handleLanguageSelect = (language) => {
    setSelectedLanguage(language);
    // Store the selection in localStorage for persistence
    localStorage.setItem('selectedLanguage', JSON.stringify(language));
    // Navigate to voice selection
    navigate('/voice', { state: { languageCode: language.code } });
  };

  const handleBack = () => {
    navigate('/');
  };

  if (!meetingContext.inMeeting) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Language Selection
            </h2>
          </div>
          <div className="px-6 py-5">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No Active Meeting
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please join a Zoom meeting to use the AI Voice Translator.</p>
                    <button
                      onClick={handleBack}
                      className="mt-2 text-yellow-800 dark:text-yellow-200 underline"
                    >
                      Go back
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Select Language
            </h2>
            <button
              onClick={handleBack}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Back
            </button>
          </div>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Choose the language for the AI translator to use
          </p>
        </div>

        <div className="px-6 py-5">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-zoom-blue"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => handleLanguageSelect(language)}
                  className={`p-4 border rounded-lg text-left transition-all ${
                    selectedLanguage?.code === language.code
                      ? 'border-zoom-blue bg-zoom-light-blue'
                      : 'border-gray-200 dark:border-gray-700 hover:border-zoom-blue hover:bg-zoom-light-blue/50'
                  }`}
                >
                  <div className="font-medium text-gray-900 dark:text-white">
                    {language.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {language.code}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LanguageSelection;

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useZoom } from '../contexts/ZoomContext';
import apiService from '../services/apiService';
import toast from 'react-hot-toast';

const Home = () => {
  const { meetingContext, refreshMeetingContext } = useZoom();
  const [isJoining, setIsJoining] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);
  const [botStatus, setBotStatus] = useState('disconnected'); // 'disconnected', 'connecting', 'connected'
  const navigate = useNavigate();

  useEffect(() => {
    // Refresh meeting context when component mounts
    refreshMeetingContext();
  }, [refreshMeetingContext]);

  const handleStartSetup = () => {
    navigate('/language');
  };

  const handleJoinMeeting = async () => {
    if (!meetingContext.meetingId) {
      toast.error('No active meeting detected');
      return;
    }

    setIsJoining(true);
    setBotStatus('connecting');

    try {
      // This would typically include language and voice preferences
      // that would be stored in state or context
      const preferences = {
        languageCode: 'en-US', // Default language
        voiceId: 'default',    // Default voice
      };

      await apiService.joinMeetingWithBot(meetingContext.meetingId, preferences);
      toast.success('AI translator bot has joined the meeting');
      setBotStatus('connected');
    } catch (error) {
      console.error('Failed to join meeting with bot:', error);
      toast.error(error.response?.data?.message || 'Failed to join meeting with bot');
      setBotStatus('disconnected');
    } finally {
      setIsJoining(false);
    }
  };

  const handleLeaveMeeting = async () => {
    if (!meetingContext.meetingId) {
      toast.error('No active meeting detected');
      return;
    }

    setIsLeaving(true);

    try {
      await apiService.leaveMeeting(meetingContext.meetingId);
      toast.success('AI translator bot has left the meeting');
      setBotStatus('disconnected');
    } catch (error) {
      console.error('Failed to leave meeting:', error);
      toast.error(error.response?.data?.message || 'Failed to disconnect bot from meeting');
    } finally {
      setIsLeaving(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            AI Voice Translator
          </h2>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Translate speech in real-time during your Zoom meetings
          </p>
        </div>

        <div className="px-6 py-5">
          {meetingContext.inMeeting ? (
            <div className="space-y-6">
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-md">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                      Meeting Detected
                    </h3>
                    <div className="mt-2 text-sm text-green-700 dark:text-green-300">
                      <p>Meeting ID: {meetingContext.meetingId}</p>
                      <p>Your role: {meetingContext.role}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex flex-col space-y-4">
                {botStatus === 'disconnected' && (
                  <>
                    <button
                      onClick={handleStartSetup}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-zoom-blue hover:bg-zoom-dark-blue focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zoom-blue"
                    >
                      Configure Language & Voice
                    </button>
                    <button
                      onClick={handleJoinMeeting}
                      disabled={isJoining}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isJoining ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Connecting...
                        </>
                      ) : (
                        'Join Meeting with AI Translator'
                      )}
                    </button>
                  </>
                )}

                {botStatus === 'connecting' && (
                  <div className="text-center py-4">
                    <div className="animate-spin mx-auto h-8 w-8 border-t-2 border-b-2 border-zoom-blue rounded-full"></div>
                    <p className="mt-2 text-sm text-gray-500">Connecting AI translator to meeting...</p>
                  </div>
                )}

                {botStatus === 'connected' && (
                  <>
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                            AI Translator Active
                          </h3>
                          <div className="mt-2 text-sm text-blue-700 dark:text-blue-300">
                            <p>The AI translator is now active in your meeting.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-4">
                      <button
                        onClick={handleStartSetup}
                        className="flex-1 py-2 px-4 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zoom-blue"
                      >
                        Change Settings
                      </button>
                      <button
                        onClick={handleLeaveMeeting}
                        disabled={isLeaving}
                        className="flex-1 py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isLeaving ? 'Disconnecting...' : 'Disconnect AI Translator'}
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No Active Meeting
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please join a Zoom meeting to use the AI Voice Translator.</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Home;

import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useZoom } from '../contexts/ZoomContext';
import apiService from '../services/apiService';
import toast from 'react-hot-toast';

const VoiceSelection = () => {
  const { meetingContext } = useZoom();
  const [voices, setVoices] = useState([]);
  const [selectedVoice, setSelectedVoice] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get the language code from location state or localStorage
  const languageCode = location.state?.languageCode || 
    (localStorage.getItem('selectedLanguage') ? 
      JSON.parse(localStorage.getItem('selectedLanguage')).code : 
      'en-US');

  useEffect(() => {
    const fetchVoices = async () => {
      try {
        setIsLoading(true);
        
        // In a real app, this would fetch from your API
        // For now, we'll use mock data based on the language
        let mockVoices = [];
        
        switch (languageCode) {
          case 'en-US':
            mockVoices = [
              { id: 'en-US-1', name: 'Emma (Female)', gender: 'female' },
              { id: 'en-US-2', name: 'Michael (Male)', gender: 'male' },
              { id: 'en-US-3', name: 'Olivia (Female)', gender: 'female' },
              { id: 'en-US-4', name: 'James (Male)', gender: 'male' },
            ];
            break;
          case 'es-ES':
            mockVoices = [
              { id: 'es-ES-1', name: 'Sofia (Female)', gender: 'female' },
              { id: 'es-ES-2', name: 'Diego (Male)', gender: 'male' },
            ];
            break;
          case 'fr-FR':
            mockVoices = [
              { id: 'fr-FR-1', name: 'Camille (Female)', gender: 'female' },
              { id: 'fr-FR-2', name: 'Antoine (Male)', gender: 'male' },
            ];
            break;
          default:
            mockVoices = [
              { id: `${languageCode}-1`, name: 'Voice 1 (Female)', gender: 'female' },
              { id: `${languageCode}-2`, name: 'Voice 2 (Male)', gender: 'male' },
            ];
        }
        
        // Simulate API call
        setTimeout(() => {
          setVoices(mockVoices);
          setIsLoading(false);
        }, 500);
        
        // In a real app, you would use:
        // const response = await apiService.getVoices(languageCode);
        // setVoices(response);
      } catch (error) {
        console.error('Failed to fetch voices:', error);
        toast.error('Failed to load available voices');
        setIsLoading(false);
      }
    };

    fetchVoices();
  }, [languageCode]);

  const handleVoiceSelect = (voice) => {
    setSelectedVoice(voice);
    // Store the selection in localStorage for persistence
    localStorage.setItem('selectedVoice', JSON.stringify(voice));
  };

  const handleBack = () => {
    navigate('/language');
  };

  const handleSave = async () => {
    if (!selectedVoice) {
      toast.error('Please select a voice');
      return;
    }

    if (!meetingContext.meetingId) {
      toast.error('No active meeting detected');
      return;
    }

    setIsSaving(true);

    try {
      const selectedLanguage = JSON.parse(localStorage.getItem('selectedLanguage'));
      
      const preferences = {
        languageCode: selectedLanguage.code,
        languageName: selectedLanguage.name,
        voiceId: selectedVoice.id,
        voiceName: selectedVoice.name,
        voiceGender: selectedVoice.gender,
      };

      // In a real app, you would save these preferences to your backend
      // await apiService.setMeetingPreferences(meetingContext.meetingId, preferences);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      toast.success('Voice preferences saved successfully');
      navigate('/');
    } catch (error) {
      console.error('Failed to save preferences:', error);
      toast.error('Failed to save voice preferences');
    } finally {
      setIsSaving(false);
    }
  };

  if (!meetingContext.inMeeting) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
          <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Voice Selection
            </h2>
          </div>
          <div className="px-6 py-5">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No Active Meeting
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    <p>Please join a Zoom meeting to use the AI Voice Translator.</p>
                    <button
                      onClick={() => navigate('/')}
                      className="mt-2 text-yellow-800 dark:text-yellow-200 underline"
                    >
                      Go back to home
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Select Voice
            </h2>
            <button
              onClick={handleBack}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              Back
            </button>
          </div>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Choose the voice for the AI translator to use
          </p>
        </div>

        <div className="px-6 py-5">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-zoom-blue"></div>
            </div>
          ) : (
            <>
              <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Selected language: <span className="font-medium">{localStorage.getItem('selectedLanguage') ? JSON.parse(localStorage.getItem('selectedLanguage')).name : 'English (US)'}</span>
                </p>
              </div>
              
              <div className="grid grid-cols-1 gap-4 mb-6">
                {voices.map((voice) => (
                  <button
                    key={voice.id}
                    onClick={() => handleVoiceSelect(voice)}
                    className={`p-4 border rounded-lg text-left transition-all ${
                      selectedVoice?.id === voice.id
                        ? 'border-zoom-blue bg-zoom-light-blue'
                        : 'border-gray-200 dark:border-gray-700 hover:border-zoom-blue hover:bg-zoom-light-blue/50'
                    }`}
                  >
                    <div className="font-medium text-gray-900 dark:text-white">
                      {voice.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {voice.gender === 'female' ? 'Female Voice' : 'Male Voice'}
                    </div>
                  </button>
                ))}
              </div>
              
              <div className="flex justify-end">
                <button
                  onClick={handleSave}
                  disabled={!selectedVoice || isSaving}
                  className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-zoom-blue hover:bg-zoom-dark-blue focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-zoom-blue disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Save and Continue'
                  )}
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceSelection;

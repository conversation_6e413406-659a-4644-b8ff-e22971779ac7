import axios from 'axios';

// Create axios instance with base URL
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// API endpoints for AI voice translator
export const apiService = {
  // Get available languages
  getLanguages: async () => {
    try {
      const response = await api.get('/languages');
      return response.data;
    } catch (error) {
      console.error('Error fetching languages:', error);
      throw error;
    }
  },

  // Get available voices for a language
  getVoices: async (languageCode) => {
    try {
      const response = await api.get(`/voices/${languageCode}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching voices for language ${languageCode}:`, error);
      throw error;
    }
  },

  // Set language and voice for a meeting
  setMeetingPreferences: async (meetingId, preferences) => {
    try {
      const response = await api.post(`/meetings/${meetingId}/preferences`, preferences);
      return response.data;
    } catch (error) {
      console.error('Error setting meeting preferences:', error);
      throw error;
    }
  },

  // Join a meeting with the AI bot
  joinMeetingWithBot: async (meetingId, preferences) => {
    try {
      const response = await api.post(`/meetings/${meetingId}/join`, preferences);
      return response.data;
    } catch (error) {
      console.error('Error joining meeting with bot:', error);
      throw error;
    }
  },

  // Leave a meeting (disconnect the AI bot)
  leaveMeeting: async (meetingId) => {
    try {
      const response = await api.post(`/meetings/${meetingId}/leave`);
      return response.data;
    } catch (error) {
      console.error('Error leaving meeting:', error);
      throw error;
    }
  },
};

export default apiService;

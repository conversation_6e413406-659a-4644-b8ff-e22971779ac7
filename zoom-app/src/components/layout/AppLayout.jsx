import { useZoom } from '../../contexts/ZoomContext';

const AppLayout = ({ children }) => {
  const { isInitialized, error, meetingContext } = useZoom();

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-red-50 text-red-800">
        <div className="bg-white p-6 rounded-lg shadow-md max-w-md w-full">
          <h2 className="text-xl font-bold mb-4">Error Initializing Zoom SDK</h2>
          <p className="mb-4">{error}</p>
          <p className="text-sm">
            Please make sure you're launching this app from within Zoom.
          </p>
        </div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-zoom-blue mb-4"></div>
        <p className="text-gray-600">Initializing Zoom SDK...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                AI Voice Translator
              </h1>
              {meetingContext.meetingId && (
                <span className="ml-4 px-2 py-1 bg-zoom-light-blue text-zoom-blue text-xs rounded-md">
                  Meeting ID: {meetingContext.meetingId}
                </span>
              )}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {meetingContext.userName && (
                <span>Logged in as: {meetingContext.userName}</span>
              )}
            </div>
          </div>
        </div>
      </header>
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {children}
      </main>
    </div>
  );
};

export default AppLayout;

import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { ZoomProvider } from './contexts/ZoomContext';
import AppLayout from './components/layout/AppLayout';
import Home from './pages/Home';
import LanguageSelection from './pages/LanguageSelection';
import VoiceSelection from './pages/VoiceSelection';
import './App.css';

function App() {
  return (
    <ZoomProvider>
      <BrowserRouter>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 3000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              style: {
                background: '#22c55e',
              },
            },
            error: {
              style: {
                background: '#ef4444',
              },
              duration: 4000,
            },
          }}
        />
        <AppLayout>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/language" element={<LanguageSelection />} />
            <Route path="/voice" element={<VoiceSelection />} />
          </Routes>
        </AppLayout>
      </BrowserRouter>
    </ZoomProvider>
  );
}

export default App;

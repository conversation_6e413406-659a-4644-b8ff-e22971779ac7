/**
 * <PERSON><PERSON><PERSON> to add the translator prompt to the database
 * 
 * Usage: node scripts/add-translator-prompt.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const { translator_prompt } = require('../backend/utils/prompts');

// Load environment variables
dotenv.config();

// Import the Prompt model
const Prompt = require('../backend/models/Prompt');

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB Connected'))
.catch(err => {
  console.error('MongoDB Connection Error:', err);
  process.exit(1);
});

// Create the translator prompt
const createTranslatorPrompt = async () => {
  try {
    // Check if a translator prompt already exists
    const existingPrompt = await Prompt.findOne({
      name: 'AI Voice Translator',
      type: 'agent'
    });

    if (existingPrompt) {
      console.log('Translator prompt already exists. Updating...');
      
      // Create a JSON object with welcome and prompt
      const contentObject = {
        welcome: "Hello, I'm your AI voice translator. How can I assist you today?",
        prompt: translator_prompt
      };

      // Convert to JSON string
      const jsonContent = JSON.stringify(contentObject);
      
      // Update the existing prompt
      await Prompt.findByIdAndUpdate(
        existingPrompt._id,
        {
          content: jsonContent,
          description: 'AI Voice Translator prompt for real-time translation between languages',
          isActive: true,
          updatedAt: Date.now()
        }
      );
      
      console.log('Translator prompt updated successfully');
    } else {
      console.log('Creating new translator prompt...');
      
      // Create a JSON object with welcome and prompt
      const contentObject = {
        welcome: "Hello, I'm your AI voice translator. How can I assist you today?",
        prompt: translator_prompt
      };

      // Convert to JSON string
      const jsonContent = JSON.stringify(contentObject);
      
      // Create a new prompt
      await Prompt.create({
        name: 'AI Voice Translator',
        type: 'agent',
        content: jsonContent,
        description: 'AI Voice Translator prompt for real-time translation between languages',
        isActive: true
      });
      
      console.log('Translator prompt created successfully');
    }
    
    // Deactivate all other agent prompts
    await Prompt.updateMany(
      { 
        type: 'agent',
        name: { $ne: 'AI Voice Translator' }
      },
      { isActive: false }
    );
    
    console.log('All other agent prompts deactivated');
    
    // Disconnect from MongoDB
    mongoose.disconnect();
    console.log('MongoDB Disconnected');
  } catch (error) {
    console.error('Error creating translator prompt:', error);
    mongoose.disconnect();
    process.exit(1);
  }
};

// Run the function
createTranslatorPrompt();

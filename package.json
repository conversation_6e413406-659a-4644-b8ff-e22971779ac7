{"name": "ivr-bot", "version": "1.0.0", "description": "IVR Bot with frontend and backend", "main": "index.js", "scripts": {"frontend": "cd frontend && npm run dev", "backend": "cd backend && npm run dev", "dev": "concurrently \"npm run backend\" \"npm run frontend\"", "install-all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "keywords": ["ivr", "bot", "voice"], "author": "", "license": "MIT", "dependencies": {"concurrently": "^8.2.2"}}
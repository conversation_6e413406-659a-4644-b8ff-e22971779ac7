# AI Voice Translator

This application provides real-time voice translation services, allowing users to communicate across language barriers.

## Setup Instructions

1. Install dependencies:
   ```
   npm install
   ```

2. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Fill in the required API keys and configuration

3. Initialize the database:
   ```
   npm run db:init
   ```

4. Add the translator prompt to the database:
   ```
   node scripts/add-translator-prompt.js
   ```

5. Start the application:
   ```
   npm run dev
   ```

## Features

- Real-time voice translation
- Support for multiple languages
- High-quality voice synthesis
- Call recording and transcription
- Integration with Zoom for video conferencing

## API Keys Required

- OpenAI API key for language processing
- ElevenLabs API key for voice synthesis
- Deepgram API key for speech recognition
- Zoom API credentials for video conferencing integration

## Directory Required

- This project is only working on Linux system
- Create the directory of /zoom_bot/tmp, /zoom_bot/records
- Add the full access permission to those directories.

## Usage

1. Configure your API keys in the settings page
2. Set up your preferred voice in the Agent page
3. Start a call or join a Zoom meeting
4. The AI will automatically translate between languages in real-time

## Troubleshooting

If you encounter issues with the audio playback or downloads:
1. Check that your browser allows audio playback
2. Verify that the API keys are correctly configured
3. Check the server logs for any errors
4. Ensure the MongoDB database is running properly
